import { expect } from "chai";
import { ethers } from "hardhat";
import { time } from "@nomicfoundation/hardhat-network-helpers";
import axios from "axios";

/**
 * Price Discovery and Oracle Testing Suite
 * 
 * Comprehensive testing of price feeds, oracle accuracy, latency validation,
 * and price manipulation detection for the MEV arbitrage bot system.
 */

interface PriceSource {
  name: string;
  getPrice: (token: string) => Promise<number>;
  latency?: number;
  accuracy?: number;
}

interface PriceValidationResult {
  source: string;
  price: number;
  timestamp: number;
  latency: number;
  deviation: number;
  isValid: boolean;
}

class PriceDiscoveryTester {
  private priceSources: PriceSource[] = [];
  private mockOracle: any;
  private priceHistory: Map<string, number[]> = new Map();

  constructor() {
    this.initializePriceSources();
  }

  private initializePriceSources(): void {
    // Chainlink Price Feed Mock
    this.priceSources.push({
      name: "Chainlink",
      getPrice: async (token: string) => {
        // Simulate Chainlink price feed
        const basePrice = this.getBasePriceForToken(token);
        const variation = (Math.random() - 0.5) * 0.02; // ±1% variation
        return basePrice * (1 + variation);
      },
    });

    // Uniswap TWAP Mock
    this.priceSources.push({
      name: "Uniswap_TWAP",
      getPrice: async (token: string) => {
        const basePrice = this.getBasePriceForToken(token);
        const variation = (Math.random() - 0.5) * 0.05; // ±2.5% variation
        return basePrice * (1 + variation);
      },
    });

    // External API Mock (CoinGecko style)
    this.priceSources.push({
      name: "External_API",
      getPrice: async (token: string) => {
        const basePrice = this.getBasePriceForToken(token);
        const variation = (Math.random() - 0.5) * 0.03; // ±1.5% variation
        return basePrice * (1 + variation);
      },
    });

    // DEX Aggregator Mock
    this.priceSources.push({
      name: "DEX_Aggregator",
      getPrice: async (token: string) => {
        const basePrice = this.getBasePriceForToken(token);
        const variation = (Math.random() - 0.5) * 0.04; // ±2% variation
        return basePrice * (1 + variation);
      },
    });
  }

  private getBasePriceForToken(token: string): number {
    const basePrices: { [key: string]: number } = {
      ETH: 2000,
      BTC: 40000,
      USDC: 1,
      USDT: 1,
      MATIC: 0.8,
      AVAX: 25,
      BNB: 300,
      FTM: 0.3,
    };
    return basePrices[token] || 100;
  }

  async validatePriceAccuracy(token: string, expectedPrice: number, threshold: number = 0.05): Promise<PriceValidationResult[]> {
    const results: PriceValidationResult[] = [];

    for (const source of this.priceSources) {
      const startTime = Date.now();
      
      try {
        const price = await source.getPrice(token);
        const latency = Date.now() - startTime;
        const deviation = Math.abs(price - expectedPrice) / expectedPrice;
        const isValid = deviation <= threshold;

        results.push({
          source: source.name,
          price,
          timestamp: Date.now(),
          latency,
          deviation,
          isValid,
        });

        // Store price history
        if (!this.priceHistory.has(token)) {
          this.priceHistory.set(token, []);
        }
        this.priceHistory.get(token)!.push(price);

      } catch (error) {
        results.push({
          source: source.name,
          price: 0,
          timestamp: Date.now(),
          latency: Date.now() - startTime,
          deviation: 1,
          isValid: false,
        });
      }
    }

    return results;
  }

  async testPriceFeedLatency(token: string, targetLatency: number = 5000): Promise<boolean> {
    const latencyTests = await Promise.all(
      this.priceSources.map(async (source) => {
        const startTime = Date.now();
        await source.getPrice(token);
        return Date.now() - startTime;
      })
    );

    const averageLatency = latencyTests.reduce((sum, latency) => sum + latency, 0) / latencyTests.length;
    return averageLatency <= targetLatency;
  }

  detectPriceManipulation(token: string, windowSize: number = 10): boolean {
    const history = this.priceHistory.get(token);
    if (!history || history.length < windowSize) {
      return false;
    }

    const recentPrices = history.slice(-windowSize);
    const average = recentPrices.reduce((sum, price) => sum + price, 0) / recentPrices.length;
    
    // Check for sudden price spikes (>20% deviation from average)
    const latestPrice = recentPrices[recentPrices.length - 1];
    const deviation = Math.abs(latestPrice - average) / average;
    
    return deviation > 0.2; // 20% threshold for manipulation detection
  }

  calculatePriceConsensus(results: PriceValidationResult[]): number {
    const validPrices = results.filter(r => r.isValid).map(r => r.price);
    if (validPrices.length === 0) return 0;
    
    // Use median for consensus to avoid outliers
    validPrices.sort((a, b) => a - b);
    const mid = Math.floor(validPrices.length / 2);
    
    return validPrices.length % 2 === 0
      ? (validPrices[mid - 1] + validPrices[mid]) / 2
      : validPrices[mid];
  }
}

describe("Price Discovery and Oracle Testing", function () {
  let priceDiscovery: PriceDiscoveryTester;
  let mockOracle: any;

  before(async function () {
    priceDiscovery = new PriceDiscoveryTester();
    
    // Deploy mock oracle for testing
    const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
    mockOracle = await MockPriceOracle.deploy();
    await mockOracle.waitForDeployment();
  });

  describe("Price Feed Accuracy", function () {
    it("Should validate price accuracy within acceptable thresholds", async function () {
      const expectedETHPrice = 2000;
      const results = await priceDiscovery.validatePriceAccuracy("ETH", expectedETHPrice, 0.05);
      
      expect(results.length).to.be.greaterThan(0);
      
      const validResults = results.filter(r => r.isValid);
      const accuracyRate = validResults.length / results.length;
      
      expect(accuracyRate).to.be.greaterThan(0.8); // 80% accuracy threshold
      
      console.log(`Price accuracy rate: ${(accuracyRate * 100).toFixed(2)}%`);
      results.forEach(result => {
        console.log(`${result.source}: $${result.price.toFixed(2)} (${(result.deviation * 100).toFixed(2)}% deviation)`);
      });
    });

    it("Should handle multiple tokens with different price ranges", async function () {
      const tokens = [
        { symbol: "ETH", expectedPrice: 2000 },
        { symbol: "BTC", expectedPrice: 40000 },
        { symbol: "USDC", expectedPrice: 1 },
        { symbol: "MATIC", expectedPrice: 0.8 },
      ];

      for (const token of tokens) {
        const results = await priceDiscovery.validatePriceAccuracy(token.symbol, token.expectedPrice);
        const validResults = results.filter(r => r.isValid);
        
        expect(validResults.length).to.be.greaterThan(0, `No valid prices for ${token.symbol}`);
      }
    });

    it("Should calculate price consensus correctly", async function () {
      const results = await priceDiscovery.validatePriceAccuracy("ETH", 2000);
      const consensus = priceDiscovery.calculatePriceConsensus(results);
      
      expect(consensus).to.be.greaterThan(0);
      expect(consensus).to.be.within(1900, 2100); // Within 5% of expected price
    });
  });

  describe("Price Feed Latency", function () {
    it("Should meet latency requirements (<5 seconds)", async function () {
      const latencyTest = await priceDiscovery.testPriceFeedLatency("ETH", 5000);
      expect(latencyTest).to.be.true;
    });

    it("Should measure individual source latencies", async function () {
      const results = await priceDiscovery.validatePriceAccuracy("ETH", 2000);
      
      results.forEach(result => {
        expect(result.latency).to.be.lessThan(5000); // 5 second max
        console.log(`${result.source} latency: ${result.latency}ms`);
      });
    });

    it("Should handle concurrent price requests efficiently", async function () {
      const startTime = Date.now();
      
      const promises = Array(10).fill(0).map(() => 
        priceDiscovery.validatePriceAccuracy("ETH", 2000)
      );
      
      await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      expect(totalTime).to.be.lessThan(10000); // Should complete within 10 seconds
    });
  });

  describe("Smart Contract Price Queries", function () {
    it("Should query Chainlink price feeds correctly", async function () {
      // Set a test price
      const testPrice = ethers.parseUnits("2000", 8); // $2000 with 8 decimals
      const tokenAddress = ethers.Wallet.createRandom().address;
      
      await mockOracle.setPrice(tokenAddress, testPrice);
      
      const retrievedPrice = await mockOracle.getPrice(tokenAddress);
      expect(retrievedPrice).to.equal(testPrice);
    });

    it("Should handle price feed failures gracefully", async function () {
      const invalidTokenAddress = ethers.ZeroAddress;
      
      await expect(mockOracle.getPrice(invalidTokenAddress))
        .to.be.revertedWith("Price not set");
    });

    it("Should provide Chainlink-compatible interface", async function () {
      const tokenAddress = ethers.Wallet.createRandom().address;
      const testPrice = ethers.parseUnits("2000", 8);
      
      await mockOracle.setPrice(tokenAddress, testPrice);
      
      const [roundId, answer, startedAt, updatedAt, answeredInRound] = 
        await mockOracle.latestRoundData(tokenAddress);
      
      expect(roundId).to.equal(1);
      expect(answer).to.equal(testPrice);
      expect(updatedAt).to.be.greaterThan(0);
    });
  });

  describe("Price Manipulation Detection", function () {
    it("Should detect sudden price spikes", async function () {
      // Generate normal price history
      for (let i = 0; i < 10; i++) {
        await priceDiscovery.validatePriceAccuracy("ETH", 2000);
      }
      
      // Simulate price manipulation
      const manipulatedResults = await priceDiscovery.validatePriceAccuracy("ETH", 2500); // 25% spike
      
      const isManipulated = priceDiscovery.detectPriceManipulation("ETH");
      expect(isManipulated).to.be.true;
    });

    it("Should not flag normal price volatility", async function () {
      // Generate normal price history with small variations
      for (let i = 0; i < 10; i++) {
        const variation = 1980 + (Math.random() * 40); // ±2% variation around $2000
        await priceDiscovery.validatePriceAccuracy("BTC", variation);
      }
      
      const isManipulated = priceDiscovery.detectPriceManipulation("BTC");
      expect(isManipulated).to.be.false;
    });

    it("Should implement circuit breaker for extreme deviations", async function () {
      const tokenAddress = ethers.Wallet.createRandom().address;
      
      // Set normal price
      await mockOracle.setPrice(tokenAddress, ethers.parseUnits("2000", 8));
      
      // Simulate extreme price movement
      await mockOracle.simulatePriceMovement(tokenAddress, 50, 10); // 50% increase with 10% volatility
      
      const newPrice = await mockOracle.getPrice(tokenAddress);
      const originalPrice = ethers.parseUnits("2000", 8);
      
      // Check if circuit breaker logic would trigger
      const priceChange = Number(newPrice - originalPrice) / Number(originalPrice);
      expect(Math.abs(priceChange)).to.be.greaterThan(0.3); // Should trigger 30% circuit breaker
    });
  });

  describe("Multi-Source Price Aggregation", function () {
    it("Should aggregate prices from multiple sources", async function () {
      const results = await priceDiscovery.validatePriceAccuracy("ETH", 2000);
      
      expect(results.length).to.be.greaterThan(2); // Multiple sources
      
      const prices = results.map(r => r.price);
      const maxPrice = Math.max(...prices);
      const minPrice = Math.min(...prices);
      const spread = (maxPrice - minPrice) / minPrice;
      
      expect(spread).to.be.lessThan(0.1); // Max 10% spread between sources
    });

    it("Should weight sources by reliability", async function () {
      const results = await priceDiscovery.validatePriceAccuracy("ETH", 2000);
      
      // Chainlink should be most reliable (lowest deviation)
      const chainlinkResult = results.find(r => r.source === "Chainlink");
      expect(chainlinkResult).to.exist;
      expect(chainlinkResult!.deviation).to.be.lessThan(0.02); // <2% deviation
    });

    it("Should handle source failures gracefully", async function () {
      // This would test fallback mechanisms when sources fail
      const results = await priceDiscovery.validatePriceAccuracy("ETH", 2000);
      
      // Even if some sources fail, should have at least one valid result
      const validResults = results.filter(r => r.isValid);
      expect(validResults.length).to.be.greaterThan(0);
    });
  });

  describe("Performance Benchmarking", function () {
    it("Should meet throughput requirements", async function () {
      const startTime = Date.now();
      const iterations = 50;
      
      const promises = Array(iterations).fill(0).map(() => 
        priceDiscovery.validatePriceAccuracy("ETH", 2000)
      );
      
      await Promise.all(promises);
      const endTime = Date.now();
      
      const throughput = iterations / ((endTime - startTime) / 1000);
      expect(throughput).to.be.greaterThan(10); // >10 operations per second
      
      console.log(`Price discovery throughput: ${throughput.toFixed(2)} ops/sec`);
    });

    it("Should maintain accuracy under load", async function () {
      const concurrentTests = 20;
      const promises = Array(concurrentTests).fill(0).map(() => 
        priceDiscovery.validatePriceAccuracy("ETH", 2000, 0.05)
      );
      
      const allResults = await Promise.all(promises);
      
      // Calculate overall accuracy
      const totalResults = allResults.flat();
      const validResults = totalResults.filter(r => r.isValid);
      const accuracyRate = validResults.length / totalResults.length;
      
      expect(accuracyRate).to.be.greaterThan(0.8); // Maintain 80% accuracy under load
    });
  });
});
