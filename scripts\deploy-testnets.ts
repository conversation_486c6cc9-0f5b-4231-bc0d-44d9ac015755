import { ethers } from "hardhat";
import { Contract, Wallet } from "ethers";
import fs from "fs";
import path from "path";

/**
 * Comprehensive Testnet Deployment Script
 * 
 * Deploys all MEV arbitrage bot contracts across multiple testnets
 * with proper configuration and verification.
 */

interface DeploymentConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  explorerUrl: string;
  tokens: {
    [symbol: string]: string;
  };
  dexes: {
    [name: string]: string;
  };
  gasSettings: {
    gasPrice?: string;
    maxFeePerGas?: string;
    maxPriorityFeePerGas?: string;
  };
}

const DEPLOYMENT_CONFIGS: { [network: string]: DeploymentConfig } = {
  goerli: {
    chainId: 5,
    name: "Ethereum Goerli",
    rpcUrl: process.env.GOERLI_RPC_URL || "",
    explorerUrl: "https://goerli.etherscan.io",
    tokens: {
      WETH: "******************************************",
      USDC: "******************************************",
      USDT: "******************************************",
    },
    dexes: {
      uniswapV2Router: "******************************************",
      uniswapV3Router: "******************************************",
    },
    gasSettings: {
      gasPrice: "20000000000", // 20 gwei
    },
  },
  mumbai: {
    chainId: 80001,
    name: "Polygon Mumbai",
    rpcUrl: process.env.MUMBAI_RPC_URL || "",
    explorerUrl: "https://mumbai.polygonscan.com",
    tokens: {
      WMATIC: "******************************************",
      USDC: "******************************************",
      WETH: "******************************************",
    },
    dexes: {
      quickswapRouter: "******************************************",
    },
    gasSettings: {
      gasPrice: "1000000000", // 1 gwei
    },
  },
  bscTestnet: {
    chainId: 97,
    name: "BSC Testnet",
    rpcUrl: "https://data-seed-prebsc-1-s1.binance.org:8545/",
    explorerUrl: "https://testnet.bscscan.com",
    tokens: {
      WBNB: "******************************************",
      BUSD: "******************************************",
      USDT: "******************************************",
    },
    dexes: {
      pancakeswapRouter: "******************************************",
    },
    gasSettings: {
      gasPrice: "10000000000", // 10 gwei
    },
  },
  fuji: {
    chainId: 43113,
    name: "Avalanche Fuji",
    rpcUrl: "https://api.avax-test.network/ext/bc/C/rpc",
    explorerUrl: "https://testnet.snowtrace.io",
    tokens: {
      WAVAX: "******************************************",
      USDC: "******************************************",
      WETH: "******************************************",
    },
    dexes: {
      traderJoeRouter: "******************************************",
    },
    gasSettings: {
      gasPrice: "25000000000", // 25 gwei
    },
  },
};

interface DeploymentResult {
  network: string;
  chainId: number;
  contracts: {
    [name: string]: {
      address: string;
      transactionHash: string;
      gasUsed: string;
      deploymentCost: string;
    };
  };
  timestamp: number;
  deployer: string;
}

class TestnetDeployer {
  private deploymentResults: DeploymentResult[] = [];
  private deployer: Wallet;

  constructor(privateKey: string) {
    this.deployer = new Wallet(privateKey);
  }

  async deployToNetwork(networkName: string): Promise<DeploymentResult> {
    const config = DEPLOYMENT_CONFIGS[networkName];
    if (!config) {
      throw new Error(`Network ${networkName} not configured`);
    }

    console.log(`\n🚀 Deploying to ${config.name}...`);
    
    const provider = new ethers.JsonRpcProvider(config.rpcUrl);
    const connectedWallet = this.deployer.connect(provider);

    // Verify network connection
    const network = await provider.getNetwork();
    if (Number(network.chainId) !== config.chainId) {
      throw new Error(`Chain ID mismatch: expected ${config.chainId}, got ${network.chainId}`);
    }

    // Check deployer balance
    const balance = await provider.getBalance(connectedWallet.address);
    console.log(`Deployer balance: ${ethers.formatEther(balance)} ETH`);
    
    if (balance < ethers.parseEther("0.1")) {
      throw new Error("Insufficient balance for deployment");
    }

    const result: DeploymentResult = {
      network: networkName,
      chainId: config.chainId,
      contracts: {},
      timestamp: Date.now(),
      deployer: connectedWallet.address,
    };

    try {
      // Deploy TokenDiscovery contract
      console.log("📋 Deploying TokenDiscovery...");
      const tokenDiscovery = await this.deployContract(
        "TokenDiscovery",
        [],
        connectedWallet,
        config.gasSettings
      );
      result.contracts.TokenDiscovery = tokenDiscovery;

      // Deploy LiquidityChecker contract
      console.log("💧 Deploying LiquidityChecker...");
      const liquidityChecker = await this.deployContract(
        "LiquidityChecker",
        [],
        connectedWallet,
        config.gasSettings
      );
      result.contracts.LiquidityChecker = liquidityChecker;

      // Deploy ArbitrageExecutor contract
      console.log("⚡ Deploying ArbitrageExecutor...");
      const arbitrageExecutor = await this.deployContract(
        "ArbitrageExecutor",
        [
          tokenDiscovery.address,
          liquidityChecker.address,
          config.tokens.WETH || ethers.ZeroAddress,
          config.dexes.uniswapV2Router || config.dexes.pancakeswapRouter || ethers.ZeroAddress,
        ],
        connectedWallet,
        config.gasSettings
      );
      result.contracts.ArbitrageExecutor = arbitrageExecutor;

      // Configure contracts
      console.log("⚙️ Configuring contracts...");
      await this.configureContracts(result.contracts, config, connectedWallet);

      console.log(`✅ Deployment to ${config.name} completed successfully!`);
      this.deploymentResults.push(result);

      return result;

    } catch (error) {
      console.error(`❌ Deployment to ${config.name} failed:`, error);
      throw error;
    }
  }

  private async deployContract(
    contractName: string,
    args: any[],
    wallet: Wallet,
    gasSettings: any
  ): Promise<any> {
    const ContractFactory = await ethers.getContractFactory(contractName);
    
    const deploymentOptions: any = {
      gasLimit: 5000000,
      ...gasSettings,
    };

    const contract = await ContractFactory.connect(wallet).deploy(...args, deploymentOptions);
    const receipt = await contract.deploymentTransaction()?.wait();
    
    if (!receipt) {
      throw new Error(`Failed to deploy ${contractName}`);
    }

    const address = await contract.getAddress();
    const gasUsed = receipt.gasUsed.toString();
    const gasPrice = receipt.gasPrice || 0n;
    const deploymentCost = ethers.formatEther(gasUsed * gasPrice);

    console.log(`  ✓ ${contractName} deployed to: ${address}`);
    console.log(`  ⛽ Gas used: ${gasUsed}`);
    console.log(`  💰 Cost: ${deploymentCost} ETH`);

    return {
      address,
      transactionHash: receipt.hash,
      gasUsed,
      deploymentCost,
      contract,
    };
  }

  private async configureContracts(
    contracts: any,
    config: DeploymentConfig,
    wallet: Wallet
  ): Promise<void> {
    const { ArbitrageExecutor, TokenDiscovery } = contracts;

    // Add tokens to whitelist
    for (const [symbol, address] of Object.entries(config.tokens)) {
      if (address && address !== ethers.ZeroAddress) {
        console.log(`  Adding ${symbol} to whitelist...`);
        const tx = await TokenDiscovery.contract.addToken(
          address,
          symbol,
          true, // isWhitelisted
          { gasLimit: 200000 }
        );
        await tx.wait();
      }
    }

    // Configure flash loan providers
    console.log("  Configuring flash loan providers...");
    // This would involve setting up provider addresses for each network
    
    // Set minimum profit threshold
    console.log("  Setting profit thresholds...");
    const tx = await ArbitrageExecutor.contract.setMinProfitThreshold(
      ethers.parseEther("50"), // $50 minimum profit
      { gasLimit: 100000 }
    );
    await tx.wait();
  }

  async deployToAllNetworks(): Promise<void> {
    console.log("🌐 Starting multi-chain deployment...\n");

    const networks = Object.keys(DEPLOYMENT_CONFIGS);
    const results = [];

    for (const network of networks) {
      try {
        console.log(`\n📡 Deploying to ${DEPLOYMENT_CONFIGS[network].name}...`);
        const result = await this.deployToNetwork(network);
        results.push(result);

        if (result.success) {
          console.log(`✅ ${DEPLOYMENT_CONFIGS[network].name} deployment completed`);
        } else {
          console.log(`❌ ${DEPLOYMENT_CONFIGS[network].name} deployment failed`);
        }

        // Wait between deployments to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 3000));
      } catch (error) {
        console.error(`Failed to deploy to ${network}:`, error);
        results.push({
          network,
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Save deployment results
    await this.saveDeploymentResults(results);

    // Generate deployment report
    this.generateDeploymentReport(results);
  }

  async deployToNetwork(networkName: string): Promise<any> {
    const config = DEPLOYMENT_CONFIGS[networkName];
    if (!config) {
      throw new Error(`Network configuration not found: ${networkName}`);
    }

    const startTime = Date.now();
    const deploymentResult = {
      network: networkName,
      name: config.name,
      chainId: config.chainId,
      success: false,
      contracts: {},
      gasUsed: 0,
      totalCost: "0",
      timestamp: new Date().toISOString(),
      duration: 0,
      error: null
    };

    try {
      // Setup provider and wallet
      const provider = new ethers.JsonRpcProvider(config.rpcUrl);
      const wallet = new ethers.Wallet(process.env.PRIVATE_KEY!, provider);

      // Verify network connection
      const network = await provider.getNetwork();
      if (Number(network.chainId) !== config.chainId) {
        throw new Error(`Chain ID mismatch: expected ${config.chainId}, got ${Number(network.chainId)}`);
      }

      // Check wallet balance
      const balance = await provider.getBalance(wallet.address);
      console.log(`  💰 Deployer balance: ${ethers.formatEther(balance)} ${config.name.split(' ')[0]}`);

      if (balance < ethers.parseEther("0.01")) {
        throw new Error("Insufficient balance for deployment");
      }

      // Deploy contracts
      const contracts = await this.deployContracts(wallet, config);
      deploymentResult.contracts = contracts;

      // Verify contracts on explorer
      await this.verifyContracts(contracts, config);

      deploymentResult.success = true;
      deploymentResult.duration = Date.now() - startTime;

    } catch (error) {
      deploymentResult.error = error.message;
      console.error(`  ❌ Deployment error: ${error.message}`);
    }

    return deploymentResult;
  }

  private async deployContracts(wallet: ethers.Wallet, config: DeploymentConfig): Promise<any> {
    const contracts = {};
    let totalGasUsed = 0;

    // Deploy ArbitrageExecutor
    console.log("  📄 Deploying ArbitrageExecutor...");
    const ArbitrageExecutor = await ethers.getContractFactory("ArbitrageExecutor", wallet);
    const arbitrageExecutor = await ArbitrageExecutor.deploy({
      gasPrice: config.gasSettings.gasPrice
    });
    await arbitrageExecutor.waitForDeployment();

    const executorAddress = await arbitrageExecutor.getAddress();
    contracts['ArbitrageExecutor'] = executorAddress;
    console.log(`    ✅ ArbitrageExecutor deployed: ${executorAddress}`);

    // Deploy TokenDiscovery
    console.log("  📄 Deploying TokenDiscovery...");
    const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery", wallet);
    const tokenDiscovery = await TokenDiscovery.deploy({
      gasPrice: config.gasSettings.gasPrice
    });
    await tokenDiscovery.waitForDeployment();

    const discoveryAddress = await tokenDiscovery.getAddress();
    contracts['TokenDiscovery'] = discoveryAddress;
    console.log(`    ✅ TokenDiscovery deployed: ${discoveryAddress}`);

    // Deploy LiquidityChecker
    console.log("  📄 Deploying LiquidityChecker...");
    const LiquidityChecker = await ethers.getContractFactory("LiquidityChecker", wallet);
    const liquidityChecker = await LiquidityChecker.deploy({
      gasPrice: config.gasSettings.gasPrice
    });
    await liquidityChecker.waitForDeployment();

    const checkerAddress = await liquidityChecker.getAddress();
    contracts['LiquidityChecker'] = checkerAddress;
    console.log(`    ✅ LiquidityChecker deployed: ${checkerAddress}`);

    return contracts;
  }

  private async verifyContracts(contracts: any, config: DeploymentConfig): Promise<void> {
    console.log("  🔍 Contract verification will be handled separately...");
    // Note: Contract verification requires API keys and is network-specific
    // This would be implemented based on the specific block explorer APIs
  }

  private async saveDeploymentResults(results: any[]): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `deployments/testnet-deployment-${timestamp}.json`;

    const deploymentData = {
      timestamp: new Date().toISOString(),
      totalNetworks: results.length,
      successfulDeployments: results.filter(r => r.success).length,
      results
    };

    try {
      await fs.writeFile(filename, JSON.stringify(deploymentData, null, 2));
      console.log(`\n💾 Deployment results saved to: ${filename}`);
    } catch (error) {
      console.error("Failed to save deployment results:", error);
    }
  }

  private generateDeploymentReport(results: any[]): void {
    console.log("\n📋 Multi-Chain Deployment Report");
    console.log("=" * 50);

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    console.log(`\n📊 Summary:`);
    console.log(`  Total Networks: ${results.length}`);
    console.log(`  Successful: ${successful.length}`);
    console.log(`  Failed: ${failed.length}`);
    console.log(`  Success Rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);

    if (successful.length > 0) {
      console.log(`\n✅ Successful Deployments:`);
      successful.forEach(result => {
        console.log(`  • ${result.name} (${result.duration}ms)`);
        Object.entries(result.contracts).forEach(([name, address]) => {
          console.log(`    - ${name}: ${address}`);
        });
      });
    }

    if (failed.length > 0) {
      console.log(`\n❌ Failed Deployments:`);
      failed.forEach(result => {
        console.log(`  • ${result.name || result.network}: ${result.error}`);
      });
    }
  }

  private async saveDeploymentResults(): Promise<void> {
    const resultsDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `testnet-deployments-${timestamp}.json`;
    const filepath = path.join(resultsDir, filename);

    fs.writeFileSync(filepath, JSON.stringify(this.deploymentResults, null, 2));
    console.log(`\n📄 Deployment results saved to: ${filepath}`);
  }

  private generateDeploymentReport(): void {
    console.log("\n📊 DEPLOYMENT REPORT");
    console.log("=".repeat(50));

    let totalCost = 0;
    let successfulDeployments = 0;

    for (const result of this.deploymentResults) {
      console.log(`\n🌐 ${DEPLOYMENT_CONFIGS[result.network]?.name || result.network}`);
      console.log(`   Chain ID: ${result.chainId}`);
      console.log(`   Contracts deployed: ${Object.keys(result.contracts).length}`);
      
      let networkCost = 0;
      for (const [name, contract] of Object.entries(result.contracts)) {
        const cost = parseFloat(contract.deploymentCost);
        networkCost += cost;
        console.log(`   ${name}: ${contract.address} (${contract.deploymentCost} ETH)`);
      }
      
      console.log(`   Total cost: ${networkCost.toFixed(6)} ETH`);
      totalCost += networkCost;
      successfulDeployments++;
    }

    console.log("\n" + "=".repeat(50));
    console.log(`✅ Successful deployments: ${successfulDeployments}/${Object.keys(DEPLOYMENT_CONFIGS).length}`);
    console.log(`💰 Total deployment cost: ${totalCost.toFixed(6)} ETH`);
    console.log(`⏰ Deployment completed at: ${new Date().toISOString()}`);
  }
}

// Main deployment function
async function main() {
  const privateKey = process.env.PRIVATE_KEY;
  if (!privateKey) {
    throw new Error("PRIVATE_KEY environment variable not set");
  }

  const deployer = new TestnetDeployer(privateKey);
  
  // Get network from command line args or deploy to all
  const targetNetwork = process.argv[2];
  
  if (targetNetwork && DEPLOYMENT_CONFIGS[targetNetwork]) {
    await deployer.deployToNetwork(targetNetwork);
  } else {
    await deployer.deployToAllNetworks();
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { TestnetDeployer, DEPLOYMENT_CONFIGS };
