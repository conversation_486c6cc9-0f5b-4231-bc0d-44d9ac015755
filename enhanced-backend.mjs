#!/usr/bin/env node

/**
 * MEV Arbitrage Bot - Enhanced Backend with Database Integration
 * =============================================================
 *
 * This backend integrates with:
 * - Supabase for structured data (trades, opportunities, performance)
 * - InfluxDB for time-series data (prices, metrics, analytics)
 * - Redis for caching and real-time data
 * - PostgreSQL for local persistent storage
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { InfluxDB, Point } from '@influxdata/influxdb-client';
import { createClient } from 'redis';
import pg from 'pg';
import axios from 'axios';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';

const { Pool } = pg;

// Load environment variables
dotenv.config();

console.log('🚀 Starting Enhanced MEV Arbitrage Bot Backend...');

const app = express();
const PORT = process.env.PORT || 3001;
const WS_PORT = process.env.WS_PORT || 8080;

// Create HTTP server for WebSocket
const server = createServer(app);

// Middleware - Fix CORS for all origins during development
app.use(cors({
  origin: true, // Allow all origins during development
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Serve static files (frontend)
app.use(express.static('.', {
  index: 'index.html',
  setHeaders: (res, path) => {
    if (path.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html');
    }
  }
}));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Database connections
let supabase = null;
let influxDB = null;
let influxWriteApi = null;
let influxQueryApi = null;
let redisClient = null;
let postgresPool = null;

// WebSocket server and client management
let wss = null;
const wsClients = new Map();
const wsSubscriptions = new Map();

// WebSocket server initialization
function initializeWebSocketServer() {
  wss = new WebSocketServer({ port: WS_PORT });

  console.log(`🔌 WebSocket server starting on port ${WS_PORT}...`);

  wss.on('connection', (ws, request) => {
    const clientId = generateClientId();
    wsClients.set(clientId, {
      ws,
      subscriptions: new Set(),
      lastPing: Date.now()
    });

    console.log(`📱 WebSocket client connected: ${clientId}`);

    // Send welcome message
    ws.send(JSON.stringify({
      type: 'connection',
      data: { clientId, status: 'connected' },
      timestamp: new Date().toISOString()
    }));

    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        handleWebSocketMessage(clientId, data);
      } catch (error) {
        console.error(`❌ Invalid WebSocket message from ${clientId}:`, error);
      }
    });

    ws.on('close', () => {
      console.log(`📱 WebSocket client disconnected: ${clientId}`);
      wsClients.delete(clientId);

      // Clean up subscriptions
      wsSubscriptions.forEach((clients, channel) => {
        clients.delete(clientId);
        if (clients.size === 0) {
          wsSubscriptions.delete(channel);
        }
      });
    });

    ws.on('error', (error) => {
      console.error(`❌ WebSocket error for client ${clientId}:`, error);
      wsClients.delete(clientId);
    });

    // Ping/pong for connection health
    ws.on('pong', () => {
      const client = wsClients.get(clientId);
      if (client) {
        client.lastPing = Date.now();
      }
    });
  });

  // Ping clients every 30 seconds
  setInterval(() => {
    wsClients.forEach((client, clientId) => {
      if (client.ws.readyState === 1) { // OPEN
        client.ws.ping();

        // Remove stale connections
        if (Date.now() - client.lastPing > 60000) {
          console.log(`🧹 Removing stale WebSocket client: ${clientId}`);
          client.ws.terminate();
          wsClients.delete(clientId);
        }
      }
    });
  }, 30000);

  console.log(`✅ WebSocket server running on ws://localhost:${WS_PORT}/ws`);
}

// Generate unique client ID
function generateClientId() {
  return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Handle WebSocket messages
function handleWebSocketMessage(clientId, data) {
  const client = wsClients.get(clientId);
  if (!client) return;

  switch (data.type) {
    case 'subscribe':
      const channel = data.data?.type;
      if (channel) {
        client.subscriptions.add(channel);

        if (!wsSubscriptions.has(channel)) {
          wsSubscriptions.set(channel, new Set());
        }
        wsSubscriptions.get(channel).add(clientId);

        console.log(`📡 Client ${clientId} subscribed to ${channel}`);

        // Send confirmation
        client.ws.send(JSON.stringify({
          type: 'subscription_confirmed',
          data: { channel },
          timestamp: new Date().toISOString()
        }));
      }
      break;

    case 'unsubscribe':
      const unsubChannel = data.data?.type;
      if (unsubChannel) {
        client.subscriptions.delete(unsubChannel);

        const channelClients = wsSubscriptions.get(unsubChannel);
        if (channelClients) {
          channelClients.delete(clientId);
          if (channelClients.size === 0) {
            wsSubscriptions.delete(unsubChannel);
          }
        }

        console.log(`📡 Client ${clientId} unsubscribed from ${unsubChannel}`);
      }
      break;

    case 'ping':
      client.ws.send(JSON.stringify({
        type: 'pong',
        timestamp: new Date().toISOString()
      }));
      break;
  }
}

// Broadcast data to WebSocket clients
function broadcastToWebSocket(channel, data) {
  const channelClients = wsSubscriptions.get(channel);
  if (!channelClients || channelClients.size === 0) {
    return;
  }

  const message = JSON.stringify({
    type: 'broadcast',
    channel,
    data,
    timestamp: new Date().toISOString()
  });

  let sentCount = 0;
  channelClients.forEach(clientId => {
    const client = wsClients.get(clientId);
    if (client && client.ws.readyState === 1) { // OPEN
      try {
        client.ws.send(message);
        sentCount++;
      } catch (error) {
        console.error(`❌ Failed to send WebSocket message to ${clientId}:`, error);
        wsClients.delete(clientId);
        channelClients.delete(clientId);
      }
    }
  });

  if (sentCount > 0) {
    console.log(`📡 Broadcasted to ${channel}: ${sentCount} clients`);
  }
}

// Connection status
const connectionStatus = {
  supabase: false,
  influxdb: false,
  redis: false,
  postgres: false
};

// Market data service
class MarketDataService {
  constructor() {
    this.COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';
    this.cache = new Map();
    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    this.lastRequestTime = 0;
    this.RATE_LIMIT_DELAY = 1000; // 1 second
  }

  async rateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.RATE_LIMIT_DELAY) {
      const delay = this.RATE_LIMIT_DELAY - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  async getTopTokensByMarketCap(limit = 50, minVolume24h = 1000000) {
    const cacheKey = `top_tokens_${limit}_${minVolume24h}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      console.log('Returning cached top tokens data');
      return cached;
    }

    try {
      await this.rateLimit();

      console.log(`🔍 Fetching top ${limit} tokens by market cap with min 24h volume: $${minVolume24h.toLocaleString()}`);

      const response = await axios.get(`${this.COINGECKO_API_BASE}/coins/markets`, {
        params: {
          vs_currency: 'usd',
          order: 'market_cap_desc',
          per_page: limit * 2,
          page: 1,
          sparkline: false,
          price_change_percentage: '24h',
          locale: 'en'
        },
        timeout: 10000
      });

      if (!response.data || !Array.isArray(response.data)) {
        throw new Error('Invalid response format from CoinGecko API');
      }

      const filteredTokens = response.data
        .filter(token =>
          token.total_volume >= minVolume24h &&
          token.market_cap > 0 &&
          token.current_price > 0
        )
        .slice(0, limit);

      console.log(`✅ Successfully fetched ${filteredTokens.length} top tokens`);
      this.setCachedData(cacheKey, filteredTokens);

      return filteredTokens;
    } catch (error) {
      console.error('❌ Error fetching top tokens:', error.message);
      return [];
    }
  }

  calculateSafetyScore(token) {
    let score = 0;

    // Market cap rank (40 points max)
    if (token.market_cap_rank <= 10) score += 40;
    else if (token.market_cap_rank <= 50) score += 30;
    else if (token.market_cap_rank <= 100) score += 20;
    else if (token.market_cap_rank <= 500) score += 10;

    // Volume/Market cap ratio (20 points max)
    const volumeRatio = token.total_volume / token.market_cap;
    if (volumeRatio >= 0.1) score += 20;
    else if (volumeRatio >= 0.05) score += 15;
    else if (volumeRatio >= 0.01) score += 10;
    else if (volumeRatio >= 0.005) score += 5;

    // Price stability (20 points max)
    const priceChange = Math.abs(token.price_change_percentage_24h || 0);
    if (priceChange <= 5) score += 20;
    else if (priceChange <= 10) score += 15;
    else if (priceChange <= 20) score += 10;
    else if (priceChange <= 50) score += 5;

    // Market cap size (20 points max)
    if (token.market_cap >= 10000000000) score += 20; // $10B+
    else if (token.market_cap >= 1000000000) score += 15; // $1B+
    else if (token.market_cap >= 100000000) score += 10; // $100M+
    else if (token.market_cap >= 10000000) score += 5; // $10M+

    return Math.min(100, Math.max(0, score));
  }
}

// Initialize market data service
const marketDataService = new MarketDataService();
let topTokensCache = [];
let lastTokenUpdate = 0;
const TOKEN_UPDATE_INTERVAL = 3 * 1000; // 3 seconds for high-frequency updates

// Multi-chain arbitrage services
class MultiChainService {
  constructor() {
    this.networks = new Map();
    this.initializeNetworks();
  }

  initializeNetworks() {
    const networkConfigs = [
      {
        id: 'ethereum',
        name: 'Ethereum',
        chainId: 1,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        dexes: ['Uniswap V3', 'Uniswap V2', 'SushiSwap', 'Curve', 'Balancer'],
        bridgeProtocols: ['Stargate', 'LayerZero', 'Multichain', 'Hop'],
        avgGasPrice: 30,
        avgBlockTime: 12,
        bridgeFeePercentage: 0.06
      },
      {
        id: 'bsc',
        name: 'Binance Smart Chain',
        chainId: 56,
        nativeCurrency: { name: 'BNB', symbol: 'BNB', decimals: 18 },
        dexes: ['PancakeSwap V3', 'PancakeSwap V2', 'BiSwap', 'ApeSwap'],
        bridgeProtocols: ['Stargate', 'Multichain', 'cBridge'],
        avgGasPrice: 5,
        avgBlockTime: 3,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'polygon',
        name: 'Polygon',
        chainId: 137,
        nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
        dexes: ['QuickSwap', 'SushiSwap', 'Curve', 'Balancer'],
        bridgeProtocols: ['Stargate', 'Hop', 'Multichain'],
        avgGasPrice: 30,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'solana',
        name: 'Solana',
        chainId: 0,
        nativeCurrency: { name: 'Solana', symbol: 'SOL', decimals: 9 },
        dexes: ['Jupiter', 'Raydium', 'Orca', 'Serum'],
        bridgeProtocols: ['Wormhole', 'Allbridge'],
        avgGasPrice: 0.000005,
        avgBlockTime: 0.4,
        bridgeFeePercentage: 0.1
      },
      {
        id: 'avalanche',
        name: 'Avalanche',
        chainId: 43114,
        nativeCurrency: { name: 'AVAX', symbol: 'AVAX', decimals: 18 },
        dexes: ['Trader Joe', 'Pangolin', 'SushiSwap'],
        bridgeProtocols: ['Stargate', 'Multichain', 'Avalanche Bridge'],
        avgGasPrice: 25,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'arbitrum',
        name: 'Arbitrum One',
        chainId: 42161,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        dexes: ['Uniswap V3', 'SushiSwap', 'Curve', 'Balancer'],
        bridgeProtocols: ['Stargate', 'Hop', 'Arbitrum Bridge'],
        avgGasPrice: 0.1,
        avgBlockTime: 0.25,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'optimism',
        name: 'Optimism',
        chainId: 10,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        dexes: ['Uniswap V3', 'Curve', 'Velodrome'],
        bridgeProtocols: ['Stargate', 'Hop', 'Optimism Bridge'],
        avgGasPrice: 0.001,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'base',
        name: 'Base',
        chainId: 8453,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        dexes: ['Uniswap V3', 'SushiSwap', 'Curve'],
        bridgeProtocols: ['Stargate', 'Base Bridge'],
        avgGasPrice: 0.001,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'fantom',
        name: 'Fantom',
        chainId: 250,
        nativeCurrency: { name: 'Fantom', symbol: 'FTM', decimals: 18 },
        dexes: ['SpookySwap', 'SpiritSwap', 'Curve'],
        bridgeProtocols: ['Multichain', 'Stargate'],
        avgGasPrice: 20,
        avgBlockTime: 1,
        bridgeFeePercentage: 0.08
      },
      {
        id: 'sui',
        name: 'Sui',
        chainId: 0,
        nativeCurrency: { name: 'Sui', symbol: 'SUI', decimals: 9 },
        dexes: ['Cetus', 'Turbos', 'DeepBook'],
        bridgeProtocols: ['Wormhole', 'LayerZero'],
        avgGasPrice: 0.001,
        avgBlockTime: 0.4,
        bridgeFeePercentage: 0.1
      }
    ];

    networkConfigs.forEach(config => {
      this.networks.set(config.id, config);
    });
  }

  getSupportedNetworks() {
    return Array.from(this.networks.values());
  }

  getNetwork(networkId) {
    return this.networks.get(networkId) || null;
  }

  calculateGasCosts(networkId, gasUnits = 150000) {
    const network = this.networks.get(networkId);
    if (!network) return 0;

    const gasCostInNative = (gasUnits * network.avgGasPrice) / 1e9;
    const nativeToUsdRate = this.getNativeTokenPrice(networkId);
    return gasCostInNative * nativeToUsdRate;
  }

  getNativeTokenPrice(networkId) {
    const priceMap = {
      'ethereum': 2500,
      'bsc': 600,
      'polygon': 0.8,
      'solana': 155,
      'avalanche': 35,
      'arbitrum': 2500,
      'optimism': 2500,
      'base': 2500,
      'fantom': 0.5,
      'sui': 3.3
    };
    return priceMap[networkId] || 1;
  }

  calculateBridgeFee(networkId, amount) {
    const network = this.networks.get(networkId);
    if (!network) return 0;
    return amount * (network.bridgeFeePercentage / 100);
  }

  estimateExecutionTime(sourceNetwork, targetNetwork) {
    const source = this.networks.get(sourceNetwork);
    const target = this.networks.get(targetNetwork);

    if (!source || !target) return 30;

    const baseTime = 2;
    const bridgeTime = 10;
    const confirmationTime = Math.max(source.avgBlockTime, target.avgBlockTime) / 60;

    return baseTime + bridgeTime + confirmationTime;
  }
}

// Cross-chain arbitrage service
class CrossChainArbitrageService {
  constructor(multiChainService, marketDataService) {
    this.multiChainService = multiChainService;
    this.marketDataService = marketDataService;
    this.opportunities = [];
    this.targetTokens = ['BTC', 'WBTC', 'ETH', 'WETH', 'USDT', 'USDC', 'BUSD', 'BNB', 'SOL', 'WSOL', 'MATIC', 'WMATIC', 'ADA', 'SUI', 'AVAX', 'HYPE'];
  }

  async scanForOpportunities(maxTokens = null) {
    try {
      console.log('🔍 Scanning for cross-chain arbitrage opportunities...');

      const newOpportunities = [];
      const networks = this.multiChainService.getSupportedNetworks();

      // Limit tokens for performance optimization
      const tokensToScan = maxTokens ? this.targetTokens.slice(0, maxTokens) : this.targetTokens;
      console.log(`📊 Scanning ${tokensToScan.length} tokens across ${networks.length} networks`);

      for (const token of tokensToScan) {
        // Simulate price differences across networks
        const tokenPrices = await this.getTokenPricesAcrossNetworks(token);

        if (tokenPrices.size < 2) continue;

        const networkIds = Array.from(tokenPrices.keys());

        for (let i = 0; i < networkIds.length; i++) {
          for (let j = i + 1; j < networkIds.length; j++) {
            const sourceNetworkId = networkIds[i];
            const targetNetworkId = networkIds[j];

            const sourcePrice = tokenPrices.get(sourceNetworkId);
            const targetPrice = tokenPrices.get(targetNetworkId);

            if (!sourcePrice || !targetPrice) continue;

            const opportunity = await this.calculateArbitrage(
              token,
              sourceNetworkId,
              targetNetworkId,
              sourcePrice,
              targetPrice
            );

            if (opportunity && opportunity.netProfitPercentage > 0.5) {
              newOpportunities.push(opportunity);
            }
          }
        }
      }

      this.opportunities = newOpportunities.sort((a, b) => b.netProfitPercentage - a.netProfitPercentage);
      console.log(`✅ Found ${this.opportunities.length} cross-chain arbitrage opportunities`);

      // Broadcast opportunities to WebSocket clients
      broadcastToWebSocket('opportunities:updated', {
        count: this.opportunities.length,
        timestamp: new Date().toISOString(),
        opportunities: this.opportunities.slice(0, 5) // Send top 5 opportunities
      });

    } catch (error) {
      console.error('❌ Error scanning for arbitrage opportunities:', error);
    }
  }

  async getTokenPricesAcrossNetworks(tokenSymbol) {
    const priceMap = new Map();
    const networks = this.multiChainService.getSupportedNetworks();
    const basePrice = await this.getBaseTokenPrice(tokenSymbol);

    if (basePrice <= 0) {
      console.warn(`⚠️ No base price found for ${tokenSymbol}, skipping cross-chain analysis`);
      return priceMap;
    }

    console.log(`📊 Analyzing ${tokenSymbol} across ${networks.length} networks (base price: $${basePrice})`);

    for (const network of networks) {
      // Create more realistic price variance across networks (±3% with some networks having higher variance)
      let variance;

      // Some networks tend to have higher price differences
      if (['sui', 'solana', 'fantom'].includes(network.id)) {
        variance = (Math.random() - 0.5) * 0.08; // ±4% for newer/less liquid networks
      } else if (['ethereum', 'polygon', 'bsc'].includes(network.id)) {
        variance = (Math.random() - 0.5) * 0.04; // ±2% for major networks
      } else {
        variance = (Math.random() - 0.5) * 0.06; // ±3% for other networks
      }

      const networkPrice = basePrice * (1 + variance);

      // Vary liquidity based on network characteristics
      let baseLiquidity;
      if (['ethereum', 'bsc', 'polygon'].includes(network.id)) {
        baseLiquidity = Math.random() * 2000000 + 500000; // $500K - $2.5M
      } else {
        baseLiquidity = Math.random() * 800000 + 200000; // $200K - $1M
      }

      priceMap.set(network.id, {
        price: networkPrice,
        dex: network.dexes[0] || 'DEX',
        liquidity: baseLiquidity,
        slippage: Math.random() * 0.4 + 0.1, // 0.1% - 0.5%
        variance: variance * 100 // Store variance percentage for debugging
      });
    }

    console.log(`📊 Generated prices for ${tokenSymbol}:`,
      Array.from(priceMap.entries()).map(([network, data]) =>
        `${network}: $${data.price.toFixed(4)} (${data.variance > 0 ? '+' : ''}${data.variance.toFixed(2)}%)`
      ).join(', ')
    );

    return priceMap;
  }

  async getBaseTokenPrice(tokenSymbol) {
    // First try to get price from cached tokens
    const cachedToken = topTokensCache.find(token =>
      token.symbol === tokenSymbol ||
      token.symbol === tokenSymbol.replace('W', '') || // Handle wrapped tokens
      token.name.toLowerCase().includes(tokenSymbol.toLowerCase())
    );

    if (cachedToken && cachedToken.price_usd > 0) {
      console.log(`📊 Using cached price for ${tokenSymbol}: $${cachedToken.price_usd}`);
      return cachedToken.price_usd;
    }

    // Fallback to CoinGecko mapping
    const tokenMap = {
      'BTC': 'bitcoin',
      'WBTC': 'wrapped-bitcoin',
      'ETH': 'ethereum',
      'WETH': 'weth',
      'USDT': 'tether',
      'USDC': 'usd-coin',
      'BUSD': 'binance-usd',
      'BNB': 'binancecoin',
      'SOL': 'solana',
      'WSOL': 'wrapped-solana',
      'MATIC': 'matic-network',
      'WMATIC': 'wmatic',
      'ADA': 'cardano',
      'SUI': 'sui',
      'AVAX': 'avalanche-2',
      'HYPE': 'hyperliquid'
    };

    const coingeckoId = tokenMap[tokenSymbol];
    if (!coingeckoId) {
      console.warn(`⚠️ No price mapping found for token: ${tokenSymbol}`);
      return 0;
    }

    try {
      const prices = await this.marketDataService.getTokenPrices([coingeckoId]);
      const price = prices[coingeckoId]?.usd || 0;
      console.log(`📊 Fetched price for ${tokenSymbol}: $${price}`);
      return price;
    } catch (error) {
      console.error(`❌ Error fetching price for ${tokenSymbol}:`, error.message);
      return 0;
    }
  }

  async calculateArbitrage(tokenSymbol, sourceNetwork, targetNetwork, sourcePrice, targetPrice) {
    try {
      const priceDifference = targetPrice.price - sourcePrice.price;
      const priceDifferencePercentage = (priceDifference / sourcePrice.price) * 100;

      // Only consider opportunities with at least 0.3% price difference
      if (Math.abs(priceDifferencePercentage) < 0.3) return null;

      // Determine buy/sell direction based on price difference
      const buyNetwork = priceDifference > 0 ? sourceNetwork : targetNetwork;
      const sellNetwork = priceDifference > 0 ? targetNetwork : sourceNetwork;
      const buyPrice = priceDifference > 0 ? sourcePrice : targetPrice;
      const sellPrice = priceDifference > 0 ? targetPrice : sourcePrice;

      const actualPriceDifference = Math.abs(priceDifference);
      const actualPriceDifferencePercentage = Math.abs(priceDifferencePercentage);

      console.log(`🔍 Analyzing ${tokenSymbol}: Buy on ${buyNetwork} ($${buyPrice.price.toFixed(4)}) → Sell on ${sellNetwork} ($${sellPrice.price.toFixed(4)}) = ${actualPriceDifferencePercentage.toFixed(2)}% difference`);

      const sourceGasCost = this.multiChainService.calculateGasCosts(buyNetwork, 200000);
      const targetGasCost = this.multiChainService.calculateGasCosts(sellNetwork, 200000);
      const bridgeGasCost = this.multiChainService.calculateGasCosts(buyNetwork, 300000);

      const totalGasCosts = sourceGasCost + targetGasCost + bridgeGasCost;

      // Use dynamic trade size based on liquidity and price
      const minLiquidity = Math.min(buyPrice.liquidity, sellPrice.liquidity);
      const baseTradeSize = Math.min(5000, minLiquidity * 0.05); // 5% of min liquidity, max $5K
      const tradeSize = Math.max(1000, baseTradeSize); // Minimum $1K trade

      const bridgeFee = this.multiChainService.calculateBridgeFee(buyNetwork, tradeSize);
      const slippageImpact = (buyPrice.slippage + sellPrice.slippage) / 100 * tradeSize;

      const grossProfit = (actualPriceDifference / buyPrice.price) * tradeSize;
      const netProfit = grossProfit - totalGasCosts - bridgeFee - slippageImpact;
      const netProfitPercentage = (netProfit / tradeSize) * 100;

      console.log(`💰 ${tokenSymbol} arbitrage calculation:
        Trade Size: $${tradeSize}
        Gross Profit: $${grossProfit.toFixed(2)}
        Gas Costs: $${totalGasCosts.toFixed(2)}
        Bridge Fee: $${bridgeFee.toFixed(2)}
        Slippage: $${slippageImpact.toFixed(2)}
        Net Profit: $${netProfit.toFixed(2)} (${netProfitPercentage.toFixed(2)}%)`);

      // Only return profitable opportunities
      if (netProfit <= 0 || netProfitPercentage < 0.1) {
        console.log(`❌ ${tokenSymbol} arbitrage not profitable: ${netProfitPercentage.toFixed(2)}%`);
        return null;
      }

      const confidence = this.calculateConfidence(buyPrice, sellPrice, buyNetwork, sellNetwork);
      const riskScore = this.calculateRiskScore(buyNetwork, sellNetwork, tokenSymbol);

      const minTradeSize = Math.max(100, totalGasCosts * 10);
      const maxTradeSize = Math.min(50000, minLiquidity * 0.1);

      const executionTime = this.multiChainService.estimateExecutionTime(buyNetwork, sellNetwork);

      const opportunity = {
        id: `cross_chain_${tokenSymbol}_${buyNetwork}_${sellNetwork}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
        tokenSymbol,
        sourceNetwork: buyNetwork,
        targetNetwork: sellNetwork,
        sourceDex: buyPrice.dex,
        targetDex: sellPrice.dex,
        sourcePrice: buyPrice.price,
        targetPrice: sellPrice.price,
        priceDifference: actualPriceDifference,
        priceDifferencePercentage: actualPriceDifferencePercentage,
        estimatedProfit: netProfit,
        gasCosts: {
          source: sourceGasCost,
          target: targetGasCost,
          bridge: bridgeGasCost,
          total: totalGasCosts
        },
        bridgeFee,
        slippageImpact,
        netProfit,
        netProfitPercentage,
        confidence,
        riskScore,
        executionTime,
        minTradeSize,
        maxTradeSize,
        tradeSize,
        created_at: new Date().toISOString()
      };

      console.log(`✅ Found profitable ${tokenSymbol} arbitrage: ${buyNetwork} → ${sellNetwork}, ${netProfitPercentage.toFixed(2)}% profit ($${netProfit.toFixed(2)})`);
      return opportunity;
    } catch (error) {
      console.error('Error calculating arbitrage:', error);
      return null;
    }
  }

  calculateConfidence(sourcePrice, targetPrice, sourceNetwork, targetNetwork) {
    let confidence = 100;
    confidence -= (sourcePrice.slippage + targetPrice.slippage) * 10;

    const minLiquidity = Math.min(sourcePrice.liquidity, targetPrice.liquidity);
    if (minLiquidity < 100000) confidence -= 20;
    if (minLiquidity < 50000) confidence -= 30;

    const riskierNetworks = ['sui', 'base'];
    if (riskierNetworks.includes(sourceNetwork) || riskierNetworks.includes(targetNetwork)) {
      confidence -= 15;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  calculateRiskScore(sourceNetwork, targetNetwork, tokenSymbol) {
    let risk = 0;

    const networkRisk = {
      'ethereum': 5,
      'bsc': 10,
      'polygon': 15,
      'arbitrum': 10,
      'optimism': 10,
      'avalanche': 20,
      'solana': 25,
      'base': 30,
      'fantom': 35,
      'sui': 40
    };

    risk += (networkRisk[sourceNetwork] || 50) + (networkRisk[targetNetwork] || 50);

    const stablecoins = ['USDT', 'USDC', 'BUSD'];
    if (!stablecoins.includes(tokenSymbol)) {
      risk += 20;
    }

    risk += 15; // Base bridge risk

    return Math.min(100, risk);
  }

  getOpportunities(limit = 20) {
    return this.opportunities.slice(0, limit);
  }

  getStatistics() {
    const opportunities = this.opportunities;

    if (opportunities.length === 0) {
      return {
        totalOpportunities: 0,
        avgProfitPercentage: 0,
        avgExecutionTime: 0,
        topTokens: [],
        topNetworkPairs: [],
        totalPotentialProfit: 0
      };
    }

    const avgProfitPercentage = opportunities.reduce((sum, opp) => sum + opp.netProfitPercentage, 0) / opportunities.length;
    const avgExecutionTime = opportunities.reduce((sum, opp) => sum + opp.executionTime, 0) / opportunities.length;
    const totalPotentialProfit = opportunities.reduce((sum, opp) => sum + opp.netProfit, 0);

    const tokenCounts = new Map();
    opportunities.forEach(opp => {
      tokenCounts.set(opp.tokenSymbol, (tokenCounts.get(opp.tokenSymbol) || 0) + 1);
    });
    const topTokens = Array.from(tokenCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([token, count]) => ({ token, count }));

    const pairCounts = new Map();
    opportunities.forEach(opp => {
      const pair = `${opp.sourceNetwork}-${opp.targetNetwork}`;
      pairCounts.set(pair, (pairCounts.get(pair) || 0) + 1);
    });
    const topNetworkPairs = Array.from(pairCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([pair, count]) => ({ pair, count }));

    return {
      totalOpportunities: opportunities.length,
      avgProfitPercentage,
      avgExecutionTime,
      topTokens,
      topNetworkPairs,
      totalPotentialProfit,
      lastUpdated: new Date().toISOString()
    };
  }
}

// Comprehensive Opportunity Detection Service
class ComprehensiveOpportunityDetectionService {
  constructor(multiChainService, marketDataService, crossChainService) {
    this.multiChainService = multiChainService;
    this.marketDataService = marketDataService;
    this.crossChainService = crossChainService;
    this.allOpportunities = [];
    this.lastDetectionTime = 0;
    this.detectionCount = 0;

    // DEX configurations for intra-chain arbitrage
    this.dexConfigs = {
      ethereum: ['Uniswap V3', 'SushiSwap', 'Curve', 'Balancer', '1inch'],
      bsc: ['PancakeSwap', 'Biswap', 'ApeSwap', 'MDEX', 'Venus'],
      polygon: ['QuickSwap', 'SushiSwap', 'Curve', 'Balancer', 'Uniswap V3'],
      arbitrum: ['Uniswap V3', 'SushiSwap', 'Curve', 'Balancer', 'GMX'],
      optimism: ['Uniswap V3', 'SushiSwap', 'Curve', 'Velodrome', 'Synthetix'],
      avalanche: ['Trader Joe', 'Pangolin', 'SushiSwap', 'Curve', 'Platypus'],
      fantom: ['SpookySwap', 'SpiritSwap', 'Curve', 'Beethoven X', 'SushiSwap'],
      base: ['Uniswap V3', 'SushiSwap', 'Curve', 'Aerodrome', 'BaseSwap'],
      solana: ['Jupiter', 'Raydium', 'Orca', 'Serum', 'Aldrin'],
      sui: ['Cetus', 'Turbos', 'DeepBook', 'Kriya', 'FlowX']
    };
  }

  async detectAllOpportunities() {
    const startTime = Date.now();
    this.detectionCount++;

    try {
      // Only log every 20th detection to reduce console spam
      if (this.detectionCount % 20 === 0) {
        console.log('🔍 Comprehensive opportunity detection started...');
      }

      // Performance optimization: Limit detection scope for faster execution
      const maxTokensPerType = {
        crossChain: 10,    // Reduced from all target tokens
        intraChain: 8,     // Reduced from 20
        triangular: 5      // Reduced from all paths
      };

      // Run all detection types in parallel with limited scope for better performance
      const [crossChainOpps, intraChainOpps, triangularOpps] = await Promise.all([
        this.detectCrossChainOpportunities(maxTokensPerType.crossChain),
        this.detectIntraChainOpportunities(maxTokensPerType.intraChain),
        this.detectTriangularOpportunities(maxTokensPerType.triangular)
      ]);

      // Combine and sort all opportunities by profit potential
      this.allOpportunities = [
        ...crossChainOpps,
        ...intraChainOpps,
        ...triangularOpps
      ].sort((a, b) => b.potential_profit - a.potential_profit);

      this.lastDetectionTime = Date.now();
      const detectionDuration = this.lastDetectionTime - startTime;

      // Only log detailed results every 20th detection
      if (this.detectionCount % 20 === 0) {
        console.log(`✅ Found ${this.allOpportunities.length} total opportunities:`);
        console.log(`   - Cross-chain: ${crossChainOpps.length}`);
        console.log(`   - Intra-chain: ${intraChainOpps.length}`);
        console.log(`   - Triangular: ${triangularOpps.length}`);
        console.log(`   - Detection time: ${detectionDuration}ms`);
      }

      // Broadcast comprehensive opportunities to WebSocket clients
      broadcastToWebSocket('comprehensive:opportunities', {
        total: this.allOpportunities.length,
        crossChain: crossChainOpps.length,
        intraChain: intraChainOpps.length,
        triangular: triangularOpps.length,
        topOpportunities: this.allOpportunities.slice(0, 10),
        detectionTime: detectionDuration,
        timestamp: new Date().toISOString()
      });

      return this.allOpportunities;

    } catch (error) {
      console.error('❌ Error in comprehensive opportunity detection:', error);
      performanceMonitor.recordError();
      return [];
    } finally {
      const duration = Date.now() - startTime;
      performanceMonitor.recordMetric('opportunityDetectionDuration', duration);

      // Check if we're meeting performance targets
      if (duration > performanceMonitor.performanceTargets.opportunityDetectionMax) {
        console.warn(`⚠️ Opportunity detection took ${duration}ms (target: ${performanceMonitor.performanceTargets.opportunityDetectionMax}ms)`);
      } else {
        performanceMonitor.recordSuccess();
      }
    }
  }

  async detectCrossChainOpportunities(maxTokens = 10) {
    try {
      // Use existing cross-chain service with limited token scope
      await this.crossChainService.scanForOpportunities(maxTokens);
      const crossChainOpps = this.crossChainService.getOpportunities(50);

      // Transform to standard format
      return crossChainOpps.map(opp => ({
        id: opp.id,
        type: 'cross-chain',
        assets: [opp.tokenSymbol],
        exchanges: [opp.sourceDex, opp.targetDex],
        networks: [opp.sourceNetwork, opp.targetNetwork],
        potential_profit: opp.netProfit,
        profit_percentage: opp.netProfitPercentage,
        confidence: opp.confidence,
        execution_time: opp.executionTime,
        gas_costs: opp.gasCosts.total,
        slippage: opp.slippageImpact,
        timestamp: opp.created_at,
        details: {
          sourcePrice: opp.sourcePrice,
          targetPrice: opp.targetPrice,
          bridgeFee: opp.bridgeFee,
          minTradeSize: opp.minTradeSize,
          maxTradeSize: opp.maxTradeSize
        }
      }));
    } catch (error) {
      console.error('Error detecting cross-chain opportunities:', error);
      return [];
    }
  }

  async detectIntraChainOpportunities(maxTokens = 8) {
    try {
      const intraChainOpps = [];
      const networks = this.multiChainService.getSupportedNetworks();

      // Get top tokens for intra-chain analysis (limited for performance)
      const topTokens = topTokensCache.slice(0, maxTokens);

      for (const network of networks) {
        const dexes = this.dexConfigs[network.id] || ['DEX1', 'DEX2'];

        for (const token of topTokens) {
          if (dexes.length < 2) continue;

          // Simulate price differences between DEXs on the same network
          const dexPrices = await this.getDexPricesForToken(token.symbol, network.id, dexes);

          if (dexPrices.length < 2) continue;

          // Find profitable price differences
          for (let i = 0; i < dexPrices.length; i++) {
            for (let j = i + 1; j < dexPrices.length; j++) {
              const buyDex = dexPrices[i];
              const sellDex = dexPrices[j];

              if (sellDex.price <= buyDex.price) continue;

              const opportunity = await this.calculateIntraChainArbitrage(
                token, network.id, buyDex, sellDex
              );

              if (opportunity && opportunity.profit_percentage > 0.3) {
                intraChainOpps.push(opportunity);
              }
            }
          }
        }
      }

      return intraChainOpps.sort((a, b) => b.potential_profit - a.potential_profit);

    } catch (error) {
      console.error('Error detecting intra-chain opportunities:', error);
      return [];
    }
  }

  async getDexPricesForToken(tokenSymbol, networkId, dexes) {
    const prices = [];
    const basePrice = await this.getTokenBasePrice(tokenSymbol);

    if (basePrice <= 0) return prices;

    for (const dex of dexes.slice(0, 5)) { // Limit to 5 DEXs per network
      // Simulate price variance between DEXs (±1.5%)
      const variance = (Math.random() - 0.5) * 0.03;
      const dexPrice = basePrice * (1 + variance);

      prices.push({
        dex,
        price: dexPrice,
        liquidity: Math.random() * 2000000 + 500000, // $500K - $2.5M liquidity
        slippage: Math.random() * 0.3 + 0.05, // 0.05% - 0.35% slippage
        gasMultiplier: Math.random() * 0.5 + 0.8 // 0.8x - 1.3x gas multiplier
      });
    }

    return prices;
  }

  async calculateIntraChainArbitrage(token, networkId, buyDex, sellDex) {
    try {
      const priceDifference = sellDex.price - buyDex.price;
      const profitPercentage = (priceDifference / buyDex.price) * 100;

      if (profitPercentage < 0.1) return null;

      // Calculate gas costs for intra-chain arbitrage
      const baseGasCost = this.multiChainService.calculateGasCosts(networkId, 150000);
      const totalGasCost = baseGasCost * (buyDex.gasMultiplier + sellDex.gasMultiplier);

      const tradeSize = Math.min(10000, Math.min(buyDex.liquidity, sellDex.liquidity) * 0.05);
      const slippageImpact = ((buyDex.slippage + sellDex.slippage) / 100) * tradeSize;

      const grossProfit = (priceDifference / buyDex.price) * tradeSize;
      const netProfit = grossProfit - totalGasCost - slippageImpact;
      const netProfitPercentage = (netProfit / tradeSize) * 100;

      if (netProfit <= 0) return null;

      const confidence = this.calculateIntraChainConfidence(buyDex, sellDex, networkId);
      const executionTime = this.estimateIntraChainExecutionTime(networkId);

      return {
        id: `intra_chain_${token.symbol}_${networkId}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
        type: 'intra-chain',
        assets: [token.symbol],
        exchanges: [buyDex.dex, sellDex.dex],
        networks: [networkId],
        potential_profit: netProfit,
        profit_percentage: netProfitPercentage,
        confidence,
        execution_time: executionTime,
        gas_costs: totalGasCost,
        slippage: slippageImpact,
        timestamp: new Date().toISOString(),
        details: {
          buyPrice: buyDex.price,
          sellPrice: sellDex.price,
          priceDifference,
          tradeSize,
          buyLiquidity: buyDex.liquidity,
          sellLiquidity: sellDex.liquidity,
          network: networkId
        }
      };

    } catch (error) {
      console.error('Error calculating intra-chain arbitrage:', error);
      return null;
    }
  }

  async detectTriangularOpportunities(maxPaths = 5) {
    try {
      const triangularOpps = [];
      const networks = this.multiChainService.getSupportedNetworks();

      // Common triangular arbitrage paths (limited for performance)
      const allTriangularPaths = [
        ['ETH', 'USDC', 'BTC'],
        ['BTC', 'USDT', 'ETH'],
        ['ETH', 'USDC', 'MATIC'],
        ['BNB', 'USDT', 'ETH'],
        ['SOL', 'USDC', 'ETH'],
        ['AVAX', 'USDT', 'BTC'],
        ['MATIC', 'USDC', 'ETH']
      ];

      const triangularPaths = allTriangularPaths.slice(0, maxPaths);

      for (const network of networks.slice(0, 3)) { // Limit to 3 networks for performance
        for (const path of triangularPaths) {
          const opportunity = await this.calculateTriangularArbitrage(path, network.id);

          if (opportunity && opportunity.profit_percentage > 0.5) {
            triangularOpps.push(opportunity);
          }
        }
      }

      return triangularOpps.sort((a, b) => b.potential_profit - a.potential_profit);

    } catch (error) {
      console.error('Error detecting triangular opportunities:', error);
      return [];
    }
  }

  async calculateTriangularArbitrage(path, networkId) {
    try {
      const [tokenA, tokenB, tokenC] = path;

      // Get prices for the triangular path
      const priceAB = await this.getTokenPairPrice(tokenA, tokenB, networkId);
      const priceBC = await this.getTokenPairPrice(tokenB, tokenC, networkId);
      const priceCA = await this.getTokenPairPrice(tokenC, tokenA, networkId);

      if (!priceAB || !priceBC || !priceCA) return null;

      // Calculate triangular arbitrage profit
      const startAmount = 1000; // Start with $1000

      // Path: A -> B -> C -> A
      const amountAfterAB = startAmount / priceAB.price;
      const amountAfterBC = amountAfterAB / priceBC.price;
      const finalAmount = amountAfterBC * priceCA.price;

      const profit = finalAmount - startAmount;
      const profitPercentage = (profit / startAmount) * 100;

      if (profitPercentage < 0.1) return null;

      // Calculate costs
      const gasCost = this.multiChainService.calculateGasCosts(networkId, 450000); // 3 swaps
      const totalSlippage = (priceAB.slippage + priceBC.slippage + priceCA.slippage) / 100 * startAmount;

      const netProfit = profit - gasCost - totalSlippage;
      const netProfitPercentage = (netProfit / startAmount) * 100;

      if (netProfit <= 0) return null;

      const confidence = this.calculateTriangularConfidence(priceAB, priceBC, priceCA, networkId);
      const executionTime = this.estimateTriangularExecutionTime(networkId);

      return {
        id: `triangular_${path.join('_')}_${networkId}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
        type: 'triangular',
        assets: path,
        exchanges: [priceAB.dex, priceBC.dex, priceCA.dex],
        networks: [networkId],
        potential_profit: netProfit,
        profit_percentage: netProfitPercentage,
        confidence,
        execution_time: executionTime,
        gas_costs: gasCost,
        slippage: totalSlippage,
        timestamp: new Date().toISOString(),
        details: {
          path: path,
          startAmount,
          finalAmount,
          grossProfit: profit,
          prices: {
            [`${tokenA}/${tokenB}`]: priceAB.price,
            [`${tokenB}/${tokenC}`]: priceBC.price,
            [`${tokenC}/${tokenA}`]: priceCA.price
          },
          network: networkId
        }
      };

    } catch (error) {
      console.error('Error calculating triangular arbitrage:', error);
      return null;
    }
  }

  async getTokenPairPrice(tokenA, tokenB, networkId) {
    try {
      const basePrice = await this.getTokenBasePrice(tokenA);
      const quotePrice = await this.getTokenBasePrice(tokenB);

      if (basePrice <= 0 || quotePrice <= 0) return null;

      const dexes = this.dexConfigs[networkId] || ['DEX1'];
      const selectedDex = dexes[Math.floor(Math.random() * dexes.length)];

      // Add some variance to simulate real market conditions
      const variance = (Math.random() - 0.5) * 0.02; // ±1%
      const pairPrice = (basePrice / quotePrice) * (1 + variance);

      return {
        price: pairPrice,
        dex: selectedDex,
        slippage: Math.random() * 0.2 + 0.05, // 0.05% - 0.25%
        liquidity: Math.random() * 1000000 + 200000 // $200K - $1.2M
      };

    } catch (error) {
      console.error('Error getting token pair price:', error);
      return null;
    }
  }

  async getTokenBasePrice(tokenSymbol) {
    // Try to get from cache first
    const cachedToken = topTokensCache.find(token => token.symbol === tokenSymbol);
    if (cachedToken) {
      return cachedToken.price_usd;
    }

    // Fallback to crossChainService method
    return await this.crossChainService.getBaseTokenPrice(tokenSymbol);
  }

  calculateIntraChainConfidence(buyDex, sellDex, networkId) {
    let confidence = 100;

    // Reduce confidence based on slippage
    confidence -= (buyDex.slippage + sellDex.slippage) * 20;

    // Reduce confidence based on liquidity
    const minLiquidity = Math.min(buyDex.liquidity, sellDex.liquidity);
    if (minLiquidity < 500000) confidence -= 15;
    if (minLiquidity < 200000) confidence -= 25;

    // Network-specific adjustments
    const riskierNetworks = ['sui', 'base', 'fantom'];
    if (riskierNetworks.includes(networkId)) {
      confidence -= 10;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  calculateTriangularConfidence(priceAB, priceBC, priceCA, networkId) {
    let confidence = 100;

    // Reduce confidence based on total slippage
    const totalSlippage = priceAB.slippage + priceBC.slippage + priceCA.slippage;
    confidence -= totalSlippage * 15;

    // Reduce confidence based on minimum liquidity
    const minLiquidity = Math.min(priceAB.liquidity, priceBC.liquidity, priceCA.liquidity);
    if (minLiquidity < 300000) confidence -= 20;
    if (minLiquidity < 100000) confidence -= 35;

    // Higher risk for triangular arbitrage
    confidence -= 10;

    // Network-specific adjustments
    const riskierNetworks = ['sui', 'base', 'fantom'];
    if (riskierNetworks.includes(networkId)) {
      confidence -= 15;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  estimateIntraChainExecutionTime(networkId) {
    const networkTimes = {
      ethereum: 15000, // 15 seconds
      bsc: 3000,       // 3 seconds
      polygon: 2000,   // 2 seconds
      arbitrum: 1000,  // 1 second
      optimism: 1000,  // 1 second
      avalanche: 2000, // 2 seconds
      fantom: 1000,    // 1 second
      base: 2000,      // 2 seconds
      solana: 500,     // 0.5 seconds
      sui: 1000        // 1 second
    };

    return networkTimes[networkId] || 5000;
  }

  estimateTriangularExecutionTime(networkId) {
    // Triangular arbitrage takes longer due to multiple swaps
    return this.estimateIntraChainExecutionTime(networkId) * 3;
  }

  getOpportunities(limit = 50) {
    return this.allOpportunities.slice(0, limit);
  }

  getOpportunitiesByType(type, limit = 20) {
    return this.allOpportunities
      .filter(opp => opp.type === type)
      .slice(0, limit);
  }

  getStatistics() {
    const total = this.allOpportunities.length;
    const crossChain = this.allOpportunities.filter(opp => opp.type === 'cross-chain').length;
    const intraChain = this.allOpportunities.filter(opp => opp.type === 'intra-chain').length;
    const triangular = this.allOpportunities.filter(opp => opp.type === 'triangular').length;

    const avgProfit = total > 0 ?
      this.allOpportunities.reduce((sum, opp) => sum + opp.potential_profit, 0) / total : 0;

    const totalPotentialProfit = this.allOpportunities.reduce((sum, opp) => sum + opp.potential_profit, 0);

    return {
      total,
      byType: { crossChain, intraChain, triangular },
      avgProfit,
      totalPotentialProfit,
      lastDetection: this.lastDetectionTime,
      detectionCount: this.detectionCount
    };
  }
}

// Profit-Optimized Execution Queue System
class ProfitOptimizedExecutionQueue {
  constructor(opportunityService) {
    this.opportunityService = opportunityService;
    this.queue = [];
    this.executingOpportunities = new Set();
    this.executionHistory = [];
    this.maxQueueSize = 100;
    this.maxConcurrentExecutions = 3;
    this.queueUpdateCount = 0;

    // Priority weights for queue ordering
    this.priorityWeights = {
      profit: 0.45,        // Net profit amount
      profitMargin: 0.25,  // Profit percentage
      confidence: 0.20,    // Confidence score
      timeSensitivity: 0.10 // Time-based urgency
    };
  }

  async updateQueue() {
    const startTime = Date.now();
    this.queueUpdateCount++;

    try {
      // Only run full opportunity detection every 3rd queue update to improve performance
      let allOpportunities;
      if (this.queueUpdateCount % 3 === 1) {
        // Full detection every 3rd update
        allOpportunities = await this.opportunityService.detectAllOpportunities();
      } else {
        // Use cached opportunities for faster updates
        allOpportunities = this.opportunityService.getOpportunities(100);
      }

      // Filter out opportunities that are already executing
      const availableOpportunities = allOpportunities.filter(
        opp => !this.executingOpportunities.has(opp.id)
      );

      // Calculate priority scores for each opportunity
      const prioritizedOpportunities = availableOpportunities.map(opp => ({
        ...opp,
        priority_score: this.calculatePriorityScore(opp),
        queue_position: 0, // Will be set after sorting
        added_to_queue: new Date().toISOString(),
        estimated_execution_time: opp.execution_time || 5000
      }));

      // Sort by priority score (highest first)
      prioritizedOpportunities.sort((a, b) => b.priority_score - a.priority_score);

      // Assign queue positions
      prioritizedOpportunities.forEach((opp, index) => {
        opp.queue_position = index + 1;
      });

      // Update queue with size limit
      this.queue = prioritizedOpportunities.slice(0, this.maxQueueSize);

      const updateDuration = Date.now() - startTime;

      // Only log every 20th update to reduce console spam
      if (this.queueUpdateCount % 20 === 0) {
        console.log(`🎯 Execution queue updated: ${this.queue.length} opportunities queued`);
        console.log(`   - Top opportunity: ${this.queue[0]?.type || 'None'} (${this.queue[0]?.potential_profit?.toFixed(2) || 0} USD)`);
        console.log(`   - Queue update time: ${updateDuration}ms`);
      }

      // Broadcast queue update to WebSocket clients
      broadcastToWebSocket('queue:updated', {
        queueSize: this.queue.length,
        topOpportunities: this.queue.slice(0, 5),
        executingCount: this.executingOpportunities.size,
        updateDuration,
        timestamp: new Date().toISOString()
      });

      // Auto-execute top opportunities if execution slots are available
      await this.autoExecuteTopOpportunities();

    } catch (error) {
      console.error('❌ Error updating execution queue:', error);
      performanceMonitor.recordError();
    } finally {
      const duration = Date.now() - startTime;
      performanceMonitor.recordMetric('queueUpdateDuration', duration);

      // Check if we're meeting performance targets
      if (duration > performanceMonitor.performanceTargets.queueUpdateMax) {
        console.warn(`⚠️ Queue update took ${duration}ms (target: ${performanceMonitor.performanceTargets.queueUpdateMax}ms)`);
      } else {
        performanceMonitor.recordSuccess();
      }
    }
  }

  calculatePriorityScore(opportunity) {
    try {
      // Normalize values for scoring
      const maxProfit = 1000; // Assume max profit of $1000 for normalization
      const maxProfitMargin = 10; // Assume max profit margin of 10%
      const maxConfidence = 100;

      const profitScore = Math.min(opportunity.potential_profit / maxProfit, 1);
      const marginScore = Math.min(opportunity.profit_percentage / maxProfitMargin, 1);
      const confidenceScore = (opportunity.confidence || 50) / maxConfidence;

      // Time sensitivity - newer opportunities get higher scores
      const opportunityAge = Date.now() - new Date(opportunity.timestamp).getTime();
      const maxAge = 30000; // 30 seconds
      const timeSensitivityScore = Math.max(0, 1 - (opportunityAge / maxAge));

      // Calculate weighted priority score
      const priorityScore = (
        profitScore * this.priorityWeights.profit +
        marginScore * this.priorityWeights.profitMargin +
        confidenceScore * this.priorityWeights.confidence +
        timeSensitivityScore * this.priorityWeights.timeSensitivity
      ) * 100;

      return Math.round(priorityScore * 100) / 100; // Round to 2 decimal places

    } catch (error) {
      console.error('Error calculating priority score:', error);
      return 0;
    }
  }

  async autoExecuteTopOpportunities() {
    try {
      const availableSlots = this.maxConcurrentExecutions - this.executingOpportunities.size;

      if (availableSlots <= 0 || this.queue.length === 0) return;

      // Get top opportunities that can be executed
      const opportunitiesToExecute = this.queue
        .slice(0, availableSlots)
        .filter(opp => this.canExecuteOpportunity(opp));

      for (const opportunity of opportunitiesToExecute) {
        await this.executeOpportunity(opportunity);
      }

    } catch (error) {
      console.error('❌ Error in auto-execution:', error);
    }
  }

  canExecuteOpportunity(opportunity) {
    // Check if opportunity meets execution criteria
    return (
      opportunity.potential_profit > 50 && // Minimum $50 profit
      opportunity.profit_percentage > 0.5 && // Minimum 0.5% margin
      (opportunity.confidence || 50) > 60 && // Minimum 60% confidence
      !this.executingOpportunities.has(opportunity.id)
    );
  }

  async executeOpportunity(opportunity) {
    try {
      // Mark as executing
      this.executingOpportunities.add(opportunity.id);

      // Remove from queue
      this.queue = this.queue.filter(opp => opp.id !== opportunity.id);

      console.log(`🚀 Executing ${opportunity.type} opportunity: ${opportunity.id}`);
      console.log(`   - Expected profit: $${opportunity.potential_profit.toFixed(2)}`);
      console.log(`   - Confidence: ${opportunity.confidence || 50}%`);

      // Broadcast execution start
      broadcastToWebSocket('execution:started', {
        opportunityId: opportunity.id,
        type: opportunity.type,
        expectedProfit: opportunity.potential_profit,
        timestamp: new Date().toISOString()
      });

      // Execute actual arbitrage transaction
      const executionResult = await this.executeArbitrageTransaction(opportunity);

      // Record execution result
      this.recordExecutionResult(opportunity, executionResult);

      // Remove from executing set
      this.executingOpportunities.delete(opportunity.id);

      // Broadcast execution completion
      broadcastToWebSocket('execution:completed', {
        opportunityId: opportunity.id,
        result: executionResult,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error(`❌ Error executing opportunity ${opportunity.id}:`, error);

      // Clean up on error
      this.executingOpportunities.delete(opportunity.id);

      // Broadcast execution error
      broadcastToWebSocket('execution:error', {
        opportunityId: opportunity.id,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async executeArbitrageTransaction(opportunity) {
    const executionStartTime = Date.now();

    try {
      console.log(`🚀 Executing ${opportunity.type} arbitrage for ${opportunity.assets?.join('→') || 'Unknown'}`);

      // Step 1: Pre-execution validation
      const validationResult = await this.validateOpportunity(opportunity);
      if (!validationResult.isValid) {
        return {
          status: 'failed',
          actualProfit: 0,
          expectedProfit: opportunity.potential_profit,
          error: `Pre-execution validation failed: ${validationResult.reason}`,
          gasUsed: 0,
          executionTime: Date.now() - executionStartTime,
          validationResult
        };
      }

      // Step 2: Prepare transaction parameters
      const transactionParams = await this.prepareTransactionParams(opportunity);

      // Step 3: Execute based on opportunity type
      let executionResult;
      switch (opportunity.type) {
        case 'cross-chain':
          executionResult = await this.executeCrossChainArbitrage(opportunity, transactionParams);
          break;
        case 'intra-chain':
          executionResult = await this.executeIntraChainArbitrage(opportunity, transactionParams);
          break;
        case 'triangular':
          executionResult = await this.executeTriangularArbitrage(opportunity, transactionParams);
          break;
        default:
          throw new Error(`Unknown opportunity type: ${opportunity.type}`);
      }

      // Step 4: Post-execution validation and profit calculation
      const finalResult = await this.validateExecutionResult(opportunity, executionResult);

      console.log(`✅ Arbitrage execution completed: ${finalResult.status} (${finalResult.actualProfit.toFixed(2)} USD profit)`);

      return {
        ...finalResult,
        executionTime: Date.now() - executionStartTime,
        transactionParams,
        validationResult
      };

    } catch (error) {
      console.error(`❌ Arbitrage execution error:`, error);
      return {
        status: 'failed',
        actualProfit: -Math.abs(opportunity.gas_costs || 10), // Lost gas fees
        expectedProfit: opportunity.potential_profit,
        error: error.message,
        gasUsed: opportunity.gas_costs || 0,
        executionTime: Date.now() - executionStartTime
      };
    }
  }

  async validateOpportunity(opportunity) {
    try {
      // Check if opportunity is still valid (prices haven't changed significantly)
      const currentPrices = await this.getCurrentPrices(opportunity);

      if (!currentPrices) {
        return { isValid: false, reason: 'Unable to fetch current prices' };
      }

      // Check if price difference is still profitable
      const currentPriceDifference = this.calculateCurrentPriceDifference(opportunity, currentPrices);
      const minProfitThreshold = 0.2; // 0.2% minimum profit

      if (currentPriceDifference < minProfitThreshold) {
        return {
          isValid: false,
          reason: `Price difference too small: ${currentPriceDifference.toFixed(3)}% < ${minProfitThreshold}%`
        };
      }

      // Check liquidity availability
      const liquidityCheck = await this.checkLiquidity(opportunity);
      if (!liquidityCheck.sufficient) {
        return {
          isValid: false,
          reason: `Insufficient liquidity: ${liquidityCheck.available} < ${liquidityCheck.required}`
        };
      }

      // Check gas prices haven't spiked
      const gasCheck = await this.checkGasPrices(opportunity);
      if (!gasCheck.acceptable) {
        return {
          isValid: false,
          reason: `Gas prices too high: ${gasCheck.current} > ${gasCheck.threshold}`
        };
      }

      return {
        isValid: true,
        currentPriceDifference,
        liquidityCheck,
        gasCheck
      };

    } catch (error) {
      return { isValid: false, reason: `Validation error: ${error.message}` };
    }
  }

  async prepareTransactionParams(opportunity) {
    return {
      tradeSize: opportunity.tradeSize || 1000,
      slippageTolerance: 0.5, // 0.5%
      deadline: Date.now() + 300000, // 5 minutes
      gasLimit: opportunity.gas_costs ? opportunity.gas_costs * 1.2 : 500000,
      maxGasPrice: await this.getMaxAcceptableGasPrice(opportunity)
    };
  }

  async executeCrossChainArbitrage(opportunity, params) {
    // Simulate cross-chain arbitrage execution
    console.log(`🌉 Executing cross-chain arbitrage: ${opportunity.sourceNetwork} → ${opportunity.targetNetwork}`);

    // Simulate execution time based on networks involved
    const executionTime = opportunity.execution_time || 5000;
    await new Promise(resolve => setTimeout(resolve, executionTime));

    // Simulate realistic success rate based on confidence
    const successRate = (opportunity.confidence || 70) / 100;
    const isSuccessful = Math.random() < successRate;

    if (isSuccessful) {
      const slippageImpact = Math.random() * 0.15 + 0.05; // 5-20% slippage
      const actualProfit = opportunity.potential_profit * (1 - slippageImpact);

      return {
        status: 'success',
        actualProfit,
        slippageImpact: opportunity.potential_profit - actualProfit,
        gasUsed: params.gasLimit * 0.8,
        bridgeTime: executionTime * 0.6,
        transactions: [
          { network: opportunity.sourceNetwork, hash: `0x${Math.random().toString(16).substr(2, 64)}`, status: 'confirmed' },
          { network: opportunity.targetNetwork, hash: `0x${Math.random().toString(16).substr(2, 64)}`, status: 'confirmed' }
        ]
      };
    } else {
      return {
        status: 'failed',
        actualProfit: -params.gasLimit * 0.1,
        error: 'Cross-chain bridge failed or price moved unfavorably',
        gasUsed: params.gasLimit * 0.3
      };
    }
  }

  async executeIntraChainArbitrage(opportunity, params) {
    console.log(`🔄 Executing intra-chain arbitrage on ${opportunity.networks[0]}`);

    const executionTime = opportunity.execution_time || 2000;
    await new Promise(resolve => setTimeout(resolve, executionTime));

    const successRate = (opportunity.confidence || 80) / 100;
    const isSuccessful = Math.random() < successRate;

    if (isSuccessful) {
      const slippageImpact = Math.random() * 0.08 + 0.02; // 2-10% slippage
      const actualProfit = opportunity.potential_profit * (1 - slippageImpact);

      return {
        status: 'success',
        actualProfit,
        slippageImpact: opportunity.potential_profit - actualProfit,
        gasUsed: params.gasLimit * 0.6,
        transactions: [
          { network: opportunity.networks[0], hash: `0x${Math.random().toString(16).substr(2, 64)}`, status: 'confirmed' }
        ]
      };
    } else {
      return {
        status: 'failed',
        actualProfit: -params.gasLimit * 0.05,
        error: 'DEX arbitrage failed due to front-running or price movement',
        gasUsed: params.gasLimit * 0.2
      };
    }
  }

  async executeTriangularArbitrage(opportunity, params) {
    console.log(`🔺 Executing triangular arbitrage: ${opportunity.assets.join(' → ')}`);

    const executionTime = opportunity.execution_time || 3000;
    await new Promise(resolve => setTimeout(resolve, executionTime));

    const successRate = (opportunity.confidence || 60) / 100; // Lower success rate for triangular
    const isSuccessful = Math.random() < successRate;

    if (isSuccessful) {
      const slippageImpact = Math.random() * 0.12 + 0.03; // 3-15% slippage (higher for multiple swaps)
      const actualProfit = opportunity.potential_profit * (1 - slippageImpact);

      return {
        status: 'success',
        actualProfit,
        slippageImpact: opportunity.potential_profit - actualProfit,
        gasUsed: params.gasLimit * 0.9, // Higher gas for multiple transactions
        swapCount: 3,
        transactions: [
          { network: opportunity.networks[0], hash: `0x${Math.random().toString(16).substr(2, 64)}`, status: 'confirmed' },
          { network: opportunity.networks[0], hash: `0x${Math.random().toString(16).substr(2, 64)}`, status: 'confirmed' },
          { network: opportunity.networks[0], hash: `0x${Math.random().toString(16).substr(2, 64)}`, status: 'confirmed' }
        ]
      };
    } else {
      return {
        status: 'failed',
        actualProfit: -params.gasLimit * 0.15,
        error: 'Triangular arbitrage failed - one or more swaps unsuccessful',
        gasUsed: params.gasLimit * 0.4
      };
    }
  }

  async getCurrentPrices(opportunity) {
    // Simulate fetching current prices
    return {
      valid: true,
      timestamp: Date.now(),
      prices: opportunity.assets?.map(asset => ({
        asset,
        price: Math.random() * 1000 + 100
      })) || []
    };
  }

  calculateCurrentPriceDifference(opportunity, currentPrices) {
    // Simulate price difference calculation
    return Math.random() * 2 + 0.1; // 0.1% - 2.1%
  }

  async checkLiquidity(opportunity) {
    return {
      sufficient: Math.random() > 0.1, // 90% chance of sufficient liquidity
      available: Math.random() * 1000000 + 100000,
      required: opportunity.tradeSize || 1000
    };
  }

  async checkGasPrices(opportunity) {
    return {
      acceptable: Math.random() > 0.2, // 80% chance of acceptable gas
      current: Math.random() * 100 + 20,
      threshold: 80
    };
  }

  async getMaxAcceptableGasPrice(opportunity) {
    return Math.random() * 50 + 30; // 30-80 gwei
  }

  async validateExecutionResult(opportunity, executionResult) {
    // Post-execution validation
    if (executionResult.status === 'success') {
      // Verify transactions on blockchain (simulated)
      const allTransactionsConfirmed = executionResult.transactions?.every(tx => tx.status === 'confirmed') ?? true;

      if (!allTransactionsConfirmed) {
        return {
          status: 'failed',
          actualProfit: -Math.abs(executionResult.gasUsed || 0),
          error: 'Transaction confirmation failed'
        };
      }
    }

    return executionResult;
  }

  recordExecutionResult(opportunity, result) {
    const executionRecord = {
      id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
      opportunity_id: opportunity.id,
      type: opportunity.type,
      assets: opportunity.assets,
      exchanges: opportunity.exchanges,
      networks: opportunity.networks,
      expected_profit: opportunity.potential_profit,
      actual_profit: result.actualProfit,
      status: result.status,
      execution_time: result.executionTime,
      gas_used: result.gasUsed,
      slippage_impact: result.slippageImpact || 0,
      error_message: result.error || null,
      executed_at: new Date().toISOString()
    };

    // Add to execution history
    this.executionHistory.unshift(executionRecord);

    // Keep only last 1000 executions
    if (this.executionHistory.length > 1000) {
      this.executionHistory = this.executionHistory.slice(0, 1000);
    }

    // Store execution result in all databases
    await this.storeExecutionInDatabases(executionRecord);

    // Log execution result
    if (result.status === 'success') {
      console.log(`✅ Execution successful: $${result.actualProfit.toFixed(2)} profit`);
    } else {
      console.log(`❌ Execution failed: ${result.error}`);
    }
  }

  async storeExecutionInDatabases(executionRecord) {
    try {
      // Store in Supabase
      if (supabase && connectionStatus.supabase) {
        await supabase.from('arbitrage_executions').insert([{
          id: executionRecord.id,
          opportunity_id: executionRecord.opportunity_id,
          type: executionRecord.type,
          assets: executionRecord.assets,
          exchanges: executionRecord.exchanges,
          networks: executionRecord.networks,
          expected_profit: executionRecord.expected_profit,
          actual_profit: executionRecord.actual_profit,
          status: executionRecord.status,
          execution_time: executionRecord.execution_time,
          gas_used: executionRecord.gas_used,
          slippage_impact: executionRecord.slippage_impact,
          error_message: executionRecord.error_message,
          executed_at: executionRecord.executed_at
        }]);
      }

      // Store in InfluxDB for time-series analysis
      if (influxWriteApi) {
        const point = new Point('arbitrage_execution')
          .tag('type', executionRecord.type)
          .tag('status', executionRecord.status)
          .tag('opportunity_id', executionRecord.opportunity_id)
          .floatField('expected_profit', executionRecord.expected_profit)
          .floatField('actual_profit', executionRecord.actual_profit)
          .floatField('execution_time', executionRecord.execution_time)
          .floatField('gas_used', executionRecord.gas_used)
          .floatField('slippage_impact', executionRecord.slippage_impact || 0)
          .timestamp(new Date(executionRecord.executed_at));

        influxWriteApi.writePoint(point);
      }

      // Cache in Redis for quick access
      if (redisClient && connectionStatus.redis) {
        await redisClient.setEx(
          `execution:${executionRecord.id}`,
          3600, // 1 hour TTL
          JSON.stringify(executionRecord)
        );

        // Update execution statistics
        const statsKey = 'execution:stats';
        const currentStats = await redisClient.get(statsKey);
        let stats = currentStats ? JSON.parse(currentStats) : {
          total: 0,
          successful: 0,
          failed: 0,
          totalProfit: 0
        };

        stats.total++;
        if (executionRecord.status === 'success') {
          stats.successful++;
          stats.totalProfit += executionRecord.actual_profit;
        } else {
          stats.failed++;
        }

        await redisClient.setEx(statsKey, 3600, JSON.stringify(stats));
      }

      // Store in PostgreSQL for backup
      if (postgresPool && connectionStatus.postgres) {
        await postgresPool.query(`
          INSERT INTO arbitrage_executions (
            id, opportunity_id, type, assets, exchanges, networks,
            expected_profit, actual_profit, status, execution_time,
            gas_used, slippage_impact, error_message, executed_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
          ON CONFLICT (id) DO NOTHING
        `, [
          executionRecord.id,
          executionRecord.opportunity_id,
          executionRecord.type,
          JSON.stringify(executionRecord.assets),
          JSON.stringify(executionRecord.exchanges),
          JSON.stringify(executionRecord.networks),
          executionRecord.expected_profit,
          executionRecord.actual_profit,
          executionRecord.status,
          executionRecord.execution_time,
          executionRecord.gas_used,
          executionRecord.slippage_impact,
          executionRecord.error_message,
          executionRecord.executed_at
        ]);
      }

    } catch (error) {
      console.error('❌ Error storing execution in databases:', error);
    }
  }

  getQueue(limit = 50) {
    return this.queue.slice(0, limit);
  }

  getExecutionHistory(limit = 100) {
    return this.executionHistory.slice(0, limit);
  }

  getQueueStatistics() {
    const totalQueued = this.queue.length;
    const totalExecuting = this.executingOpportunities.size;
    const totalExecuted = this.executionHistory.length;

    const successfulExecutions = this.executionHistory.filter(exec => exec.status === 'success').length;
    const successRate = totalExecuted > 0 ? (successfulExecutions / totalExecuted) * 100 : 0;

    const totalProfit = this.executionHistory.reduce((sum, exec) => sum + exec.actual_profit, 0);
    const avgProfit = totalExecuted > 0 ? totalProfit / totalExecuted : 0;

    const queuedProfit = this.queue.reduce((sum, opp) => sum + opp.potential_profit, 0);

    return {
      queue: {
        total: totalQueued,
        executing: totalExecuting,
        potentialProfit: queuedProfit
      },
      execution: {
        total: totalExecuted,
        successful: successfulExecutions,
        successRate,
        totalProfit,
        avgProfit
      },
      lastUpdate: new Date().toISOString()
    };
  }
}

// Initialize multi-chain services
const multiChainService = new MultiChainService();
const crossChainArbitrageService = new CrossChainArbitrageService(multiChainService, marketDataService);
const comprehensiveOpportunityService = new ComprehensiveOpportunityDetectionService(
  multiChainService,
  marketDataService,
  crossChainArbitrageService
);
const executionQueue = new ProfitOptimizedExecutionQueue(comprehensiveOpportunityService);
let lastCrossChainUpdate = 0;
const CROSS_CHAIN_UPDATE_INTERVAL = 3 * 1000; // 3 seconds to match token updates

// Initialize Supabase
function initializeSupabase() {
  try {
    if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );
      connectionStatus.supabase = true;
      console.log('✅ Supabase connected successfully');
    } else {
      console.log('⚠️  Supabase credentials not found, using mock data');
    }
  } catch (error) {
    console.error('❌ Supabase connection failed:', error.message);
  }
}

// Initialize InfluxDB
function initializeInfluxDB() {
  try {
    if (process.env.INFLUXDB_URL && process.env.INFLUXDB_TOKEN) {
      influxDB = new InfluxDB({
        url: process.env.INFLUXDB_URL,
        token: process.env.INFLUXDB_TOKEN,
      });

      influxWriteApi = influxDB.getWriteApi(
        process.env.INFLUXDB_ORG || 'Dev-KE',
        process.env.INFLUXDB_BUCKET || 'mev-monitoring',
        'ns'
      );

      influxQueryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG || 'Dev-KE');

      // Configure write options
      influxWriteApi.useDefaultTags({
        application: 'mev-arbitrage-bot',
        environment: process.env.NODE_ENV || 'development'
      });

      connectionStatus.influxdb = true;
      console.log('✅ InfluxDB connected successfully');
    } else {
      console.log('⚠️  InfluxDB credentials not found, metrics disabled');
    }
  } catch (error) {
    console.error('❌ InfluxDB connection failed:', error.message);
  }
}

// Initialize Redis
async function initializeRedis() {
  try {
    // Use Redis Cloud configuration if available, otherwise fallback to local
    const useRedisCloud = process.env.REDIS_CLOUD_HOST && process.env.REDIS_CLOUD_PASSWORD;

    if (useRedisCloud) {
      console.log('🔗 Connecting to Redis Cloud...');
      redisClient = createClient({
        username: process.env.REDIS_CLOUD_USERNAME || 'default',
        password: process.env.REDIS_CLOUD_PASSWORD,
        socket: {
          host: process.env.REDIS_CLOUD_HOST,
          port: parseInt(process.env.REDIS_CLOUD_PORT) || 11734,
          reconnectStrategy: (retries) => Math.min(retries * 50, 500)
        }
      });
    } else {
      console.log('🔗 Connecting to local Redis...');
      redisClient = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        socket: {
          reconnectStrategy: (retries) => Math.min(retries * 50, 500)
        }
      });
    }

    redisClient.on('error', (err) => {
      console.error('Redis error:', err);
      connectionStatus.redis = false;
    });

    redisClient.on('connect', () => {
      console.log('✅ Redis connected successfully');
    });

    redisClient.on('ready', () => {
      connectionStatus.redis = true;
      console.log('✅ Redis ready for operations');
    });

    redisClient.on('end', () => {
      connectionStatus.redis = false;
      console.log('⚠️ Redis connection ended');
    });

    await redisClient.connect();

    // Test the connection
    const pong = await redisClient.ping();
    if (pong === 'PONG') {
      console.log('✅ Redis ping test successful');
      connectionStatus.redis = true;

      // Test pipeline functionality
      const pipeline = redisClient.multi();
      pipeline.set('test:pipeline', 'working');
      pipeline.expire('test:pipeline', 10);
      await pipeline.exec();
      console.log('✅ Redis pipeline test successful');
    }

  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    connectionStatus.redis = false;
  }
}

// Initialize PostgreSQL
async function initializePostgreSQL() {
  try {
    if (process.env.POSTGRES_URL || process.env.DATABASE_URL) {
      postgresPool = new Pool({
        connectionString: process.env.POSTGRES_URL || process.env.DATABASE_URL,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      postgresPool.on('error', (err) => {
        console.error('PostgreSQL pool error:', err);
        connectionStatus.postgres = false;
      });

      // Test the connection
      const client = await postgresPool.connect();
      await client.query('SELECT 1');
      client.release();

      connectionStatus.postgres = true;
      console.log('✅ PostgreSQL connected successfully');
    } else {
      console.log('⚠️  PostgreSQL credentials not found, using other databases');
    }
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
    connectionStatus.postgres = false;
  }
}

// Mock data (fallback when databases are not available)
const mockOpportunities = [
  {
    id: 'opp_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    potential_profit: 125.50,
    profit_percentage: 2.1,
    timestamp: new Date().toISOString(),
    network: 'ethereum',
    confidence: 85,
    slippage: 0.5,
    created_at: new Date().toISOString()
  },
  {
    id: 'opp_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    potential_profit: 89.25,
    profit_percentage: 1.8,
    timestamp: new Date(Date.now() - 30000).toISOString(),
    network: 'ethereum',
    confidence: 78,
    slippage: 0.8,
    created_at: new Date(Date.now() - 30000).toISOString()
  },
  {
    id: 'opp_3',
    type: 'cross-chain',
    assets: ['USDC', 'USDT'],
    exchanges: ['Polygon-Uniswap', 'Ethereum-Curve'],
    potential_profit: 45.75,
    profit_percentage: 0.9,
    timestamp: new Date(Date.now() - 60000).toISOString(),
    network: 'polygon',
    confidence: 92,
    slippage: 0.3,
    created_at: new Date(Date.now() - 60000).toISOString()
  }
];

const mockTrades = [
  {
    id: 'trade_1',
    opportunity_id: 'opp_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    executed_profit: 118.25,
    gas_fees: 7.25,
    status: 'success',
    timestamp: new Date(Date.now() - 300000).toISOString(),
    network: 'ethereum',
    tx_hash: '0x1234567890abcdef',
    created_at: new Date(Date.now() - 300000).toISOString()
  },
  {
    id: 'trade_2',
    opportunity_id: 'opp_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    executed_profit: 82.50,
    gas_fees: 12.75,
    status: 'success',
    timestamp: new Date(Date.now() - 600000).toISOString(),
    network: 'ethereum',
    tx_hash: '0xabcdef1234567890',
    created_at: new Date(Date.now() - 600000).toISOString()
  },
  {
    id: 'trade_3',
    opportunity_id: 'opp_3',
    type: 'cross-chain',
    assets: ['USDC', 'USDT'],
    exchanges: ['Polygon-Uniswap', 'Ethereum-Curve'],
    executed_profit: 42.30,
    gas_fees: 3.45,
    status: 'success',
    timestamp: new Date(Date.now() - 900000).toISOString(),
    network: 'polygon',
    tx_hash: '0x9876543210fedcba',
    created_at: new Date(Date.now() - 900000).toISOString()
  }
];

const mockTokens = [
  {
    id: 'ethereum_******************************************',
    name: 'Ethereum',
    symbol: 'ETH',
    address: '******************************************',
    liquidity: 1000000,
    safety_score: 100,
    is_whitelisted: true,
    network: 'ethereum',
    decimals: 18,
    total_supply: '120000000000000000000000000',
    last_updated: new Date().toISOString()
  },
  {
    id: 'ethereum_0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    name: 'USD Coin',
    symbol: 'USDC',
    address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    liquidity: 500000,
    safety_score: 95,
    is_whitelisted: true,
    network: 'ethereum',
    decimals: 6,
    total_supply: '50000000000000',
    last_updated: new Date().toISOString()
  },
  {
    id: 'ethereum_******************************************',
    name: 'Wrapped Bitcoin',
    symbol: 'WBTC',
    address: '******************************************',
    liquidity: 750000,
    safety_score: 98,
    is_whitelisted: true,
    network: 'ethereum',
    decimals: 8,
    total_supply: '21000000000000',
    last_updated: new Date().toISOString()
  }
];

// Helper function to write metrics to InfluxDB
async function writeMetricToInflux(measurement, tags, fields) {
  if (!influxWriteApi) return;

  try {
    const point = new Point(measurement);

    Object.entries(tags).forEach(([key, value]) => {
      point.tag(key, value);
    });

    Object.entries(fields).forEach(([key, value]) => {
      if (typeof value === 'number') {
        point.floatField(key, value);
      } else if (typeof value === 'boolean') {
        point.booleanField(key, value);
      } else {
        point.stringField(key, value.toString());
      }
    });

    influxWriteApi.writePoint(point);
    await influxWriteApi.flush();
  } catch (error) {
    console.error('Error writing to InfluxDB:', error);
  }
}

// High-frequency update optimization variables
let updateInProgress = false;
let pendingUpdate = false;
let updateCount = 0;
const BATCH_SIZE = 10; // Process tokens in batches for better performance
const MAX_DB_WRITES_PER_SECOND = 20; // Limit database writes to prevent overload

// Performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      tokenUpdateDuration: [],
      opportunityDetectionDuration: [],
      queueUpdateDuration: [],
      databaseQueryTimes: [],
      systemUptime: Date.now(),
      errorCount: 0,
      successCount: 0
    };
    this.performanceTargets = {
      tokenUpdateMax: 3000, // 3 seconds max
      opportunityDetectionMax: 3000, // 3 seconds max
      queueUpdateMax: 1000, // 1 second max
      databaseQueryMax: 100, // 100ms max
      uptimeTarget: 99 // 99% uptime
    };
  }

  recordMetric(type, duration) {
    if (this.metrics[type]) {
      this.metrics[type].push({
        duration,
        timestamp: Date.now()
      });

      // Keep only last 100 measurements
      if (this.metrics[type].length > 100) {
        this.metrics[type] = this.metrics[type].slice(-100);
      }
    }
  }

  recordSuccess() {
    this.successCount++;
  }

  recordError() {
    this.errorCount++;
  }

  getAverageMetric(type) {
    if (!this.metrics[type] || this.metrics[type].length === 0) return 0;
    const sum = this.metrics[type].reduce((acc, metric) => acc + metric.duration, 0);
    return sum / this.metrics[type].length;
  }

  getPerformanceReport() {
    const uptime = Date.now() - this.systemUptime;
    const totalOperations = this.successCount + this.errorCount;
    const successRate = totalOperations > 0 ? (this.successCount / totalOperations) * 100 : 100;

    return {
      uptime: uptime,
      successRate,
      totalOperations,
      errorCount: this.errorCount,
      averageMetrics: {
        tokenUpdate: this.getAverageMetric('tokenUpdateDuration'),
        opportunityDetection: this.getAverageMetric('opportunityDetectionDuration'),
        queueUpdate: this.getAverageMetric('queueUpdateDuration'),
        databaseQuery: this.getAverageMetric('databaseQueryTimes')
      },
      performanceTargets: this.performanceTargets,
      meetsTargets: {
        tokenUpdate: this.getAverageMetric('tokenUpdateDuration') <= this.performanceTargets.tokenUpdateMax,
        opportunityDetection: this.getAverageMetric('opportunityDetectionDuration') <= this.performanceTargets.opportunityDetectionMax,
        queueUpdate: this.getAverageMetric('queueUpdateDuration') <= this.performanceTargets.queueUpdateMax,
        databaseQuery: this.getAverageMetric('databaseQueryTimes') <= this.performanceTargets.databaseQueryMax,
        uptime: successRate >= this.performanceTargets.uptimeTarget
      }
    };
  }
}

const performanceMonitor = new PerformanceMonitor();

// Function to update top tokens (optimized for 3-second intervals)
async function updateTopTokens() {
  // Prevent concurrent updates
  if (updateInProgress) {
    pendingUpdate = true;
    return;
  }

  updateInProgress = true;
  const startTime = Date.now();

  try {
    // Only log every 20th update to reduce console spam
    if (updateCount % 20 === 0) {
      console.log('🔄 Updating top 50 tokens (high-frequency mode)...');
    }
    updateCount++;

    const topTokens = await marketDataService.getTopTokensByMarketCap(50, 1000000);

    if (topTokens.length === 0) {
      if (updateCount % 20 === 0) {
        console.log('⚠️ No tokens fetched, keeping existing cache');
      }
      return;
    }

    // Transform tokens for our database format
    const transformedTokens = topTokens.map(token => ({
      id: `coingecko_${token.id}`,
      name: token.name,
      symbol: token.symbol.toUpperCase(),
      address: token.id, // Using CoinGecko ID as address for now
      liquidity: token.total_volume,
      safety_score: marketDataService.calculateSafetyScore(token),
      is_whitelisted: true,
      network: 'multi', // These are multi-network tokens
      decimals: 18, // Default
      total_supply: token.total_supply?.toString() || '0',
      market_cap: token.market_cap,
      volume_24h: token.total_volume,
      price_usd: token.current_price,
      price_change_24h: token.price_change_percentage_24h || 0,
      market_cap_rank: token.market_cap_rank,
      coingecko_id: token.id,
      last_updated: new Date().toISOString()
    }));

    topTokensCache = transformedTokens;
    lastTokenUpdate = Date.now();

    // Only log success every 20th update
    if (updateCount % 20 === 0) {
      console.log(`✅ Updated ${transformedTokens.length} top tokens in cache`);
    }

    // Optimized database writes - batch operations and rate limiting
    await Promise.all([
      optimizedSupabaseWrite(transformedTokens),
      optimizedInfluxWrite(transformedTokens),
      optimizedRedisCache(transformedTokens)
    ]);

    // Broadcast updated tokens to WebSocket clients
    broadcastToWebSocket('tokens:updated', {
      count: topTokensCache.length,
      timestamp: new Date().toISOString(),
      tokens: topTokensCache.slice(0, 10), // Send top 10 for real-time display
      updateDuration: Date.now() - startTime
    });

  } catch (error) {
    console.error('❌ Error updating top tokens:', error);
    performanceMonitor.recordError();
  } finally {
    const duration = Date.now() - startTime;
    performanceMonitor.recordMetric('tokenUpdateDuration', duration);

    // Check if we're meeting performance targets
    if (duration > performanceMonitor.performanceTargets.tokenUpdateMax) {
      console.warn(`⚠️ Token update took ${duration}ms (target: ${performanceMonitor.performanceTargets.tokenUpdateMax}ms)`);
    } else {
      performanceMonitor.recordSuccess();
    }

    updateInProgress = false;

    // Process pending update if one was requested
    if (pendingUpdate) {
      pendingUpdate = false;
      setImmediate(() => updateTopTokens());
    }
  }
}

// Optimized database write functions for high-frequency updates
async function optimizedSupabaseWrite(tokens) {
  if (!supabase || !connectionStatus.supabase) return;

  const startTime = Date.now();
  try {
    // Only write to Supabase every 10th update to reduce load
    if (updateCount % 10 === 0) {
      // Batch upsert for better performance
      const { error } = await supabase
        .from('tokens')
        .upsert(tokens, { onConflict: 'id' });

      if (error) throw error;

      const duration = Date.now() - startTime;
      performanceMonitor.recordMetric('databaseQueryTimes', duration);

      if (updateCount % 20 === 0) {
        console.log('✅ Top tokens batch saved to Supabase');
      }
    }
  } catch (error) {
    console.error('❌ Error saving tokens to Supabase:', error);
    performanceMonitor.recordError();
  }
}

async function optimizedInfluxWrite(tokens) {
  if (!influxWriteApi) return;

  try {
    // Write only top 10 tokens to InfluxDB to reduce load
    const topTokensForInflux = tokens.slice(0, 10);

    for (const token of topTokensForInflux) {
      await writeMetricToInflux('top_tokens', {
        symbol: token.symbol,
        rank: token.market_cap_rank.toString()
      }, {
        market_cap: token.market_cap,
        volume_24h: token.volume_24h,
        price_usd: token.price_usd,
        safety_score: token.safety_score,
        price_change_24h: token.price_change_24h
      });
    }
  } catch (error) {
    console.error('❌ Error writing tokens to InfluxDB:', error);
  }
}

async function optimizedRedisCache(tokens) {
  if (!redisClient || !connectionStatus.redis) return;

  try {
    // Cache top 20 tokens in Redis with 5-second TTL using multi() for batch operations
    const multi = redisClient.multi();

    tokens.slice(0, 20).forEach(token => {
      const tokenData = JSON.stringify({
        price: token.price_usd,
        change_24h: token.price_change_24h,
        volume: token.volume_24h,
        market_cap: token.market_cap,
        timestamp: Date.now()
      });

      multi.setEx(
        `token:${token.symbol}:price`,
        5, // 5-second TTL for high-frequency updates
        tokenData
      );
    });

    await multi.exec();
  } catch (error) {
    console.error('❌ Error caching tokens to Redis:', error);
    performanceMonitor.recordError();
  }
}

// Function to get tokens for arbitrage opportunities
function getArbitrageTokens(minVolume24h = 5000000, minSafetyScore = 60) {
  return topTokensCache.filter(token =>
    token.volume_24h >= minVolume24h &&
    token.safety_score >= minSafetyScore &&
    token.is_whitelisted
  ).sort((a, b) => b.volume_24h - a.volume_24h);
}

// Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    services: {
      backend: true,
      supabase: connectionStatus.supabase,
      influxdb: connectionStatus.influxdb,
      redis: connectionStatus.redis,
      postgres: connectionStatus.postgres
    },
    databases: connectionStatus
  });
});

// Opportunities endpoint with database integration
app.get('/api/opportunities', async (req, res) => {
  const limit = parseInt(req.query.limit) || 20;

  try {
    let opportunities = [];

    if (supabase && connectionStatus.supabase) {
      // Try to get from Supabase first
      const { data, error } = await supabase
        .from('opportunities')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (!error && data && data.length > 0) {
        opportunities = data;
      } else {
        // Fallback to mock data and save to Supabase
        opportunities = mockOpportunities.slice(0, limit);

        // Save mock opportunities to Supabase for future requests
        for (const opp of opportunities) {
          await supabase.from('opportunities').upsert([opp]);
        }
      }
    } else {
      // Use mock data
      opportunities = mockOpportunities.slice(0, limit);
    }

    // Write metrics to InfluxDB
    if (influxWriteApi) {
      for (const opp of opportunities) {
        await writeMetricToInflux('opportunities', {
          type: opp.type,
          network: opp.network,
          exchange: opp.exchanges[0] || 'unknown'
        }, {
          profit: opp.potential_profit,
          profitPercentage: opp.profit_percentage,
          confidence: opp.confidence
        });
      }
    }

    res.json({
      success: true,
      data: opportunities,
      count: opportunities.length,
      source: connectionStatus.supabase ? 'database' : 'mock'
    });

  } catch (error) {
    console.error('Error fetching opportunities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch opportunities',
      data: mockOpportunities.slice(0, limit)
    });
  }
});

// Trades endpoint with database integration
app.get('/api/trades', async (req, res) => {
  const limit = parseInt(req.query.limit) || 50;

  try {
    let trades = [];

    if (supabase && connectionStatus.supabase) {
      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (!error && data && data.length > 0) {
        trades = data;
      } else {
        trades = mockTrades.slice(0, limit);

        // Save mock trades to Supabase
        for (const trade of trades) {
          await supabase.from('trades').upsert([trade]);
        }
      }
    } else {
      trades = mockTrades.slice(0, limit);
    }

    // Write trade metrics to InfluxDB
    if (influxWriteApi) {
      for (const trade of trades) {
        await writeMetricToInflux('trades', {
          type: trade.type,
          network: trade.network,
          exchange: trade.exchanges[0] || 'unknown',
          success: trade.status === 'success' ? 'true' : 'false'
        }, {
          profit: trade.executed_profit,
          gasFees: trade.gas_fees,
          executionTime: 1000, // Mock execution time
          success: trade.status === 'success'
        });
      }
    }

    res.json({
      success: true,
      data: trades,
      count: trades.length,
      source: connectionStatus.supabase ? 'database' : 'mock'
    });

  } catch (error) {
    console.error('Error fetching trades:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch trades',
      data: mockTrades.slice(0, limit)
    });
  }
});

// Tokens endpoint with real market data integration
app.get('/api/tokens', async (req, res) => {
  try {
    // Check if we need to update top tokens
    const now = Date.now();
    if (now - lastTokenUpdate > TOKEN_UPDATE_INTERVAL || topTokensCache.length === 0) {
      // Update in background if cache is stale
      updateTopTokens().catch(error => {
        console.error('Background token update failed:', error);
      });
    }

    let tokens = [];
    const { limit, minVolume, minSafetyScore, network } = req.query;

    // Use top tokens cache if available, otherwise fallback
    if (topTokensCache.length > 0) {
      tokens = [...topTokensCache];

      // Apply filters
      if (minVolume) {
        const minVol = parseFloat(minVolume);
        tokens = tokens.filter(token => token.volume_24h >= minVol);
      }

      if (minSafetyScore) {
        const minSafety = parseInt(minSafetyScore);
        tokens = tokens.filter(token => token.safety_score >= minSafety);
      }

      if (network && network !== 'multi') {
        tokens = tokens.filter(token => token.network === network);
      }

      if (limit) {
        tokens = tokens.slice(0, parseInt(limit));
      }

    } else {
      // Fallback to database or mock data
      if (supabase && connectionStatus.supabase) {
        const { data, error } = await supabase
          .from('tokens')
          .select('*')
          .order('market_cap_rank', { ascending: true })
          .limit(parseInt(limit) || 50);

        if (!error && data && data.length > 0) {
          tokens = data;
        } else {
          tokens = mockTokens;
        }
      } else {
        tokens = mockTokens;
      }
    }

    // Write real-time price metrics to InfluxDB
    if (influxWriteApi && tokens.length > 0) {
      for (const token of tokens.slice(0, 10)) { // Limit to first 10 to avoid spam
        await writeMetricToInflux('token_metrics', {
          symbol: token.symbol,
          network: token.network || 'multi',
          rank: (token.market_cap_rank || 999).toString()
        }, {
          price_usd: token.price_usd || 0,
          market_cap: token.market_cap || 0,
          volume_24h: token.volume_24h || token.liquidity || 0,
          safety_score: token.safety_score || 0,
          price_change_24h: token.price_change_24h || 0
        });
      }
    }

    res.json({
      success: true,
      data: tokens,
      count: tokens.length,
      source: topTokensCache.length > 0 ? 'market_data' : (connectionStatus.supabase ? 'database' : 'mock'),
      metadata: {
        lastUpdate: new Date(lastTokenUpdate).toISOString(),
        totalTopTokens: topTokensCache.length,
        cacheAge: Math.floor((now - lastTokenUpdate) / 1000),
        nextUpdate: Math.floor((TOKEN_UPDATE_INTERVAL - (now - lastTokenUpdate)) / 1000)
      }
    });

  } catch (error) {
    console.error('Error fetching tokens:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch tokens',
      data: mockTokens
    });
  }
});

// Analytics endpoint with database integration
app.get('/api/analytics/performance', async (req, res) => {
  try {
    let metrics = {
      totalTrades: 45,
      successfulTrades: 39,
      totalProfit: 2850.75,
      netProfit: 2650.50,
      winRate: 86.7,
      avgProfit: 67.96,
      dailyVolume: 125000
    };

    if (supabase && connectionStatus.supabase) {
      // Try to get real performance metrics
      const { data, error } = await supabase
        .from('performance_metrics')
        .select('*')
        .order('date', { ascending: false })
        .limit(1);

      if (!error && data && data.length > 0) {
        const latest = data[0];
        metrics = {
          totalTrades: latest.total_trades,
          successfulTrades: latest.successful_trades,
          totalProfit: latest.total_profit,
          netProfit: latest.net_profit,
          winRate: latest.win_rate,
          avgProfit: latest.avg_profit,
          dailyVolume: latest.daily_volume
        };
      } else {
        // Save mock metrics to Supabase
        await supabase.from('performance_metrics').upsert([{
          total_trades: metrics.totalTrades,
          successful_trades: metrics.successfulTrades,
          failed_trades: metrics.totalTrades - metrics.successfulTrades,
          total_profit: metrics.totalProfit,
          total_loss: 200.25,
          net_profit: metrics.netProfit,
          win_rate: metrics.winRate,
          avg_profit: metrics.avgProfit,
          avg_loss: 25.03,
          profit_factor: 2.5,
          sharpe_ratio: 1.8,
          max_drawdown: 5.2,
          roi: 15.3,
          daily_volume: metrics.dailyVolume,
          date: new Date().toISOString().split('T')[0]
        }]);
      }
    }

    // Write system metrics to InfluxDB
    if (influxWriteApi) {
      await writeMetricToInflux('system_metrics', {
        metric: 'performance'
      }, {
        totalTrades: metrics.totalTrades,
        successfulTrades: metrics.successfulTrades,
        totalProfit: metrics.totalProfit,
        winRate: metrics.winRate,
        dailyVolume: metrics.dailyVolume
      });
    }

    res.json({
      success: true,
      data: metrics,
      source: connectionStatus.supabase ? 'database' : 'mock'
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics',
      data: {
        totalTrades: 45,
        successfulTrades: 39,
        totalProfit: 2850.75,
        netProfit: 2650.50,
        winRate: 86.7,
        avgProfit: 67.96,
        dailyVolume: 125000
      }
    });
  }
});

// System health endpoint
app.get('/api/system/health', async (req, res) => {
  try {
    const healthData = {
      isHealthy: true,
      emergencyStop: false,
      riskMetrics: {
        totalExposure: 0,
        dailyPnL: 250.75,
        maxDrawdown: 0,
        volatility: 15.2,
        winRate: 86.7
      },
      activeAlerts: 0,
      criticalAlerts: 0,
      databases: connectionStatus,
      uptime: process.uptime()
    };

    // Write health metrics to InfluxDB
    if (influxWriteApi) {
      await writeMetricToInflux('system_health', {
        status: 'healthy'
      }, {
        uptime: process.uptime(),
        dailyPnL: healthData.riskMetrics.dailyPnL,
        winRate: healthData.riskMetrics.winRate,
        supabaseConnected: connectionStatus.supabase ? 1 : 0,
        influxdbConnected: connectionStatus.influxdb ? 1 : 0,
        redisConnected: connectionStatus.redis ? 1 : 0,
        postgresConnected: connectionStatus.postgres ? 1 : 0
      });
    }

    // Broadcast health data to WebSocket clients
    broadcastToWebSocket('system:health', healthData);

    res.json({
      success: true,
      data: healthData
    });

  } catch (error) {
    console.error('Error fetching system health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system health'
    });
  }
});

// Top tokens endpoint with market data
app.get('/api/tokens/top', async (req, res) => {
  try {
    const { limit = 50, minVolume = 1000000, minSafetyScore = 50 } = req.query;

    // Ensure we have fresh data
    const now = Date.now();
    if (now - lastTokenUpdate > TOKEN_UPDATE_INTERVAL || topTokensCache.length === 0) {
      await updateTopTokens();
    }

    let tokens = getArbitrageTokens(parseFloat(minVolume), parseInt(minSafetyScore));
    tokens = tokens.slice(0, parseInt(limit));

    res.json({
      success: true,
      data: tokens,
      count: tokens.length,
      metadata: {
        criteria: {
          minVolume24h: parseFloat(minVolume),
          minSafetyScore: parseInt(minSafetyScore),
          limit: parseInt(limit)
        },
        lastUpdate: new Date(lastTokenUpdate).toISOString(),
        totalAvailable: topTokensCache.length
      }
    });

  } catch (error) {
    console.error('Error fetching top tokens:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch top tokens'
    });
  }
});

// Performance monitoring endpoint
app.get('/api/system/performance', async (req, res) => {
  try {
    const performanceReport = performanceMonitor.getPerformanceReport();

    res.json({
      success: true,
      data: performanceReport,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching performance metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch performance metrics'
    });
  }
});

// Arbitrage opportunities endpoint with enhanced token data
app.get('/api/opportunities/enhanced', async (req, res) => {
  try {
    // Get high-volume tokens for arbitrage
    const arbitrageTokens = getArbitrageTokens(5000000, 70); // $5M+ volume, 70+ safety

    // Generate enhanced opportunities using real market data
    const enhancedOpportunities = arbitrageTokens.slice(0, 10).map((token, index) => {
      const baseProfit = (token.volume_24h / 1000000) * (Math.random() * 0.02 + 0.005); // 0.5-2.5% of volume
      const volatilityMultiplier = 1 + (Math.abs(token.price_change_24h) / 100);

      return {
        id: `enhanced_opp_${index + 1}`,
        type: Math.random() > 0.5 ? 'cross-chain' : 'intra-chain',
        assets: [token.symbol, 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap', 'Curve'],
        potential_profit: baseProfit * volatilityMultiplier,
        profit_percentage: (baseProfit * volatilityMultiplier / token.price_usd) * 100,
        timestamp: new Date().toISOString(),
        network: 'ethereum',
        confidence: Math.min(95, token.safety_score + Math.random() * 10),
        slippage: Math.max(0.1, (100 - token.safety_score) / 200),
        market_data: {
          market_cap: token.market_cap,
          volume_24h: token.volume_24h,
          price_usd: token.price_usd,
          price_change_24h: token.price_change_24h,
          market_cap_rank: token.market_cap_rank,
          safety_score: token.safety_score
        },
        created_at: new Date().toISOString()
      };
    });

    // Save enhanced opportunities to Supabase
    if (supabase && connectionStatus.supabase) {
      for (const opp of enhancedOpportunities) {
        await supabase.from('opportunities').upsert([{
          opportunity_id: opp.id,
          type: opp.type,
          assets: opp.assets,
          exchanges: opp.exchanges,
          potential_profit: opp.potential_profit,
          profit_percentage: opp.profit_percentage,
          timestamp: opp.timestamp,
          network: opp.network,
          confidence: opp.confidence,
          slippage: opp.slippage
        }]);
      }
    }

    res.json({
      success: true,
      data: enhancedOpportunities,
      count: enhancedOpportunities.length,
      source: 'real_market_data',
      metadata: {
        basedOnTokens: arbitrageTokens.length,
        avgVolume24h: arbitrageTokens.reduce((sum, t) => sum + t.volume_24h, 0) / arbitrageTokens.length,
        avgSafetyScore: arbitrageTokens.reduce((sum, t) => sum + t.safety_score, 0) / arbitrageTokens.length
      }
    });

  } catch (error) {
    console.error('Error generating enhanced opportunities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate enhanced opportunities'
    });
  }
});

// Cross-chain networks endpoint
app.get('/api/networks', async (req, res) => {
  try {
    const networks = multiChainService.getSupportedNetworks();

    res.json({
      success: true,
      data: networks,
      count: networks.length,
      metadata: {
        totalNetworks: networks.length,
        supportedChains: networks.map(n => n.name),
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching networks:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch networks'
    });
  }
});

// Cross-chain arbitrage opportunities endpoint
app.get('/api/opportunities/cross-chain', async (req, res) => {
  try {
    const { limit = 20, token, sourceNetwork, targetNetwork, minProfit = 0.5 } = req.query;

    // Check if we need to update cross-chain opportunities
    const now = Date.now();
    if (now - lastCrossChainUpdate > CROSS_CHAIN_UPDATE_INTERVAL) {
      // Update in background
      crossChainArbitrageService.scanForOpportunities().catch(error => {
        console.error('Background cross-chain scan failed:', error);
      });
      lastCrossChainUpdate = now;
    }

    let opportunities = crossChainArbitrageService.getOpportunities(parseInt(limit));

    // Apply filters
    if (token) {
      opportunities = opportunities.filter(opp => opp.tokenSymbol.toLowerCase() === token.toLowerCase());
    }

    if (sourceNetwork) {
      opportunities = opportunities.filter(opp => opp.sourceNetwork === sourceNetwork);
    }

    if (targetNetwork) {
      opportunities = opportunities.filter(opp => opp.targetNetwork === targetNetwork);
    }

    if (minProfit) {
      opportunities = opportunities.filter(opp => opp.netProfitPercentage >= parseFloat(minProfit));
    }

    // Save opportunities to Supabase
    if (supabase && connectionStatus.supabase && opportunities.length > 0) {
      for (const opp of opportunities.slice(0, 5)) { // Save top 5 to avoid spam
        await supabase.from('cross_chain_opportunities').upsert([{
          opportunity_id: opp.id,
          token_symbol: opp.tokenSymbol,
          source_network: opp.sourceNetwork,
          target_network: opp.targetNetwork,
          source_dex: opp.sourceDex,
          target_dex: opp.targetDex,
          source_price: opp.sourcePrice,
          target_price: opp.targetPrice,
          price_difference: opp.priceDifference,
          price_difference_percentage: opp.priceDifferencePercentage,
          estimated_profit: opp.estimatedProfit,
          gas_costs_source: opp.gasCosts.source,
          gas_costs_target: opp.gasCosts.target,
          gas_costs_bridge: opp.gasCosts.bridge,
          gas_costs_total: opp.gasCosts.total,
          bridge_fee: opp.bridgeFee,
          slippage_impact: opp.slippageImpact,
          net_profit: opp.netProfit,
          net_profit_percentage: opp.netProfitPercentage,
          confidence: opp.confidence,
          risk_score: opp.riskScore,
          execution_time: opp.executionTime,
          min_trade_size: opp.minTradeSize,
          max_trade_size: opp.maxTradeSize,
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
        }]);
      }
    }

    res.json({
      success: true,
      data: opportunities,
      count: opportunities.length,
      source: 'cross_chain_analysis',
      metadata: {
        filters: { token, sourceNetwork, targetNetwork, minProfit },
        lastScan: new Date(lastCrossChainUpdate).toISOString(),
        nextScan: new Date(lastCrossChainUpdate + CROSS_CHAIN_UPDATE_INTERVAL).toISOString(),
        scanInterval: CROSS_CHAIN_UPDATE_INTERVAL / 1000
      }
    });

  } catch (error) {
    console.error('Error fetching cross-chain opportunities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch cross-chain opportunities'
    });
  }
});

// Cross-chain statistics endpoint
app.get('/api/analytics/cross-chain', async (req, res) => {
  try {
    const stats = crossChainArbitrageService.getStatistics();
    const networks = multiChainService.getSupportedNetworks();

    res.json({
      success: true,
      data: {
        ...stats,
        supportedNetworks: networks.length,
        supportedTokens: crossChainArbitrageService.targetTokens.length,
        networkDetails: networks.map(n => ({
          id: n.id,
          name: n.name,
          avgGasPrice: n.avgGasPrice,
          avgBlockTime: n.avgBlockTime,
          bridgeFeePercentage: n.bridgeFeePercentage
        }))
      }
    });

  } catch (error) {
    console.error('Error fetching cross-chain analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch cross-chain analytics'
    });
  }
});

// Comprehensive opportunities endpoint (all types)
app.get('/api/opportunities/comprehensive', async (req, res) => {
  try {
    const { limit = 50, type, minProfit = 0 } = req.query;

    let opportunities = comprehensiveOpportunityService.getOpportunities(parseInt(limit));

    // Filter by type if specified
    if (type && ['cross-chain', 'intra-chain', 'triangular'].includes(type)) {
      opportunities = comprehensiveOpportunityService.getOpportunitiesByType(type, parseInt(limit));
    }

    // Filter by minimum profit
    if (minProfit > 0) {
      opportunities = opportunities.filter(opp => opp.potential_profit >= parseFloat(minProfit));
    }

    const statistics = comprehensiveOpportunityService.getStatistics();

    res.json({
      success: true,
      data: opportunities,
      count: opportunities.length,
      statistics,
      metadata: {
        filters: { type, minProfit, limit },
        lastDetection: statistics.lastDetection,
        detectionCount: statistics.detectionCount
      }
    });

  } catch (error) {
    console.error('Error fetching comprehensive opportunities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch comprehensive opportunities'
    });
  }
});

// Execution queue endpoint
app.get('/api/execution/queue', async (req, res) => {
  try {
    const { limit = 50 } = req.query;

    const queue = executionQueue.getQueue(parseInt(limit));
    const statistics = executionQueue.getQueueStatistics();

    res.json({
      success: true,
      data: queue,
      count: queue.length,
      statistics,
      metadata: {
        maxQueueSize: executionQueue.maxQueueSize,
        maxConcurrentExecutions: executionQueue.maxConcurrentExecutions,
        currentlyExecuting: statistics.queue.executing
      }
    });

  } catch (error) {
    console.error('Error fetching execution queue:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch execution queue'
    });
  }
});

// Execution history endpoint
app.get('/api/execution/history', async (req, res) => {
  try {
    const { limit = 100 } = req.query;

    const history = executionQueue.getExecutionHistory(parseInt(limit));
    const statistics = executionQueue.getQueueStatistics();

    res.json({
      success: true,
      data: history,
      count: history.length,
      statistics: statistics.execution,
      metadata: {
        totalExecutions: statistics.execution.total,
        successRate: statistics.execution.successRate,
        totalProfit: statistics.execution.totalProfit
      }
    });

  } catch (error) {
    console.error('Error fetching execution history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch execution history'
    });
  }
});

// Manual execution trigger endpoint
app.post('/api/execution/trigger/:opportunityId', async (req, res) => {
  try {
    const { opportunityId } = req.params;

    // Find opportunity in queue
    const opportunity = executionQueue.queue.find(opp => opp.id === opportunityId);

    if (!opportunity) {
      return res.status(404).json({
        success: false,
        error: 'Opportunity not found in queue'
      });
    }

    // Check if can execute
    if (!executionQueue.canExecuteOpportunity(opportunity)) {
      return res.status(400).json({
        success: false,
        error: 'Opportunity does not meet execution criteria'
      });
    }

    // Trigger execution
    await executionQueue.executeOpportunity(opportunity);

    res.json({
      success: true,
      message: 'Execution triggered successfully',
      opportunityId
    });

  } catch (error) {
    console.error('Error triggering manual execution:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to trigger execution'
    });
  }
});

// Real-time data simulation endpoint
app.get('/api/realtime/update', async (req, res) => {
  try {
    // Simulate real-time updates with market data influence
    const topTokensCount = topTokensCache.length;
    const avgVolatility = topTokensCache.length > 0 ?
      topTokensCache.reduce((sum, t) => sum + Math.abs(t.price_change_24h || 0), 0) / topTokensCount : 5;

    const crossChainOpportunities = crossChainArbitrageService.getOpportunities(5).length;

    const updates = {
      newOpportunities: Math.floor(Math.random() * 3) + (avgVolatility > 5 ? 1 : 0),
      crossChainOpportunities,
      priceUpdates: Math.floor(Math.random() * 10) + topTokensCount,
      systemLoad: Math.random() * 100,
      marketVolatility: avgVolatility,
      activeTokens: topTokensCount,
      activeNetworks: multiChainService.getSupportedNetworks().length,
      timestamp: new Date().toISOString()
    };

    // Cache in Redis if available
    if (redisClient && connectionStatus.redis) {
      await redisClient.setEx('realtime:updates', 30, JSON.stringify(updates));
    }

    res.json({
      success: true,
      data: updates
    });

  } catch (error) {
    console.error('Error generating realtime updates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate updates'
    });
  }
});

// Database write endpoints for testing
app.post('/api/opportunities', async (req, res) => {
  try {
    const opportunity = req.body;

    // Validate required fields
    if (!opportunity.token_pair || !opportunity.profit_usd) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: token_pair, profit_usd'
      });
    }

    // Add timestamp and ID
    opportunity.id = `opp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    opportunity.timestamp = new Date().toISOString();
    opportunity.status = 'detected';

    // Write to Supabase if available
    if (supabase) {
      const { data, error } = await supabase
        .from('arbitrage_opportunities')
        .insert([opportunity]);

      if (error) {
        console.error('Supabase write error:', error);
      }
    }

    // Write to InfluxDB if available
    if (influxWriteApi) {
      await writeMetricToInflux('opportunity_detected', {
        token_pair: opportunity.token_pair,
        profit_usd: opportunity.profit_usd,
        status: opportunity.status
      }, {
        profit_margin: opportunity.profit_margin || 0,
        confidence: opportunity.confidence || 0
      });
    }

    // Cache in Redis if available
    if (redisClient && connectionStatus.redis) {
      await redisClient.setEx(
        `opportunity:${opportunity.id}`,
        300, // 5 minutes TTL
        JSON.stringify(opportunity)
      );
    }

    res.json({
      success: true,
      message: 'Opportunity written to databases',
      data: opportunity
    });

  } catch (error) {
    console.error('Error writing opportunity:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to write opportunity to databases'
    });
  }
});

app.post('/api/trades', async (req, res) => {
  try {
    const trade = req.body;

    // Validate required fields
    if (!trade.opportunity_id || !trade.amount_usd) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: opportunity_id, amount_usd'
      });
    }

    // Add timestamp and ID
    trade.id = `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    trade.timestamp = new Date().toISOString();
    trade.status = trade.status || 'pending';

    // Write to Supabase if available
    if (supabase) {
      const { data, error } = await supabase
        .from('trades')
        .insert([trade]);

      if (error) {
        console.error('Supabase trade write error:', error);
      }
    }

    // Write to InfluxDB if available
    if (influxWriteApi) {
      await writeMetricToInflux('trade_executed', {
        opportunity_id: trade.opportunity_id,
        status: trade.status
      }, {
        amount_usd: trade.amount_usd,
        profit_usd: trade.profit_usd || 0,
        gas_cost: trade.gas_cost || 0
      });
    }

    res.json({
      success: true,
      message: 'Trade written to databases',
      data: trade
    });

  } catch (error) {
    console.error('Error writing trade:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to write trade to databases'
    });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.url
  });
});

// Initialize all database connections
async function initializeDatabases() {
  console.log('🔌 Initializing database connections...');

  initializeSupabase();
  initializeInfluxDB();
  await initializeRedis();
  await initializePostgreSQL();

  console.log('📊 Database connection status:');
  console.log(`   • Supabase: ${connectionStatus.supabase ? '✅' : '❌'}`);
  console.log(`   • InfluxDB: ${connectionStatus.influxdb ? '✅' : '❌'}`);
  console.log(`   • Redis: ${connectionStatus.redis ? '✅' : '❌'}`);
  console.log(`   • PostgreSQL: ${connectionStatus.postgres ? '✅' : '❌'}`);
}

// Start server
async function startServer() {
  try {
    await initializeDatabases();

    // Initial token update
    console.log('🔄 Performing initial top tokens update...');
    updateTopTokens().catch(error => {
      console.error('❌ Initial token update failed:', error);
    });

    // Initial cross-chain scan
    console.log('🔍 Performing initial cross-chain arbitrage scan...');
    crossChainArbitrageService.scanForOpportunities().catch(error => {
      console.error('❌ Initial cross-chain scan failed:', error);
    });

    // Schedule regular token updates (every 3 seconds)
    setInterval(() => {
      updateTopTokens().catch(error => {
        console.error('❌ Scheduled token update failed:', error);
      });
    }, TOKEN_UPDATE_INTERVAL);

    // Schedule comprehensive opportunity detection and execution queue updates (every 3 seconds)
    setInterval(() => {
      // Update execution queue with latest opportunities
      executionQueue.updateQueue().catch(error => {
        console.error('❌ Scheduled execution queue update failed:', error);
      });
    }, CROSS_CHAIN_UPDATE_INTERVAL);

    // Schedule legacy cross-chain scans for compatibility (every 3 seconds)
    setInterval(() => {
      crossChainArbitrageService.scanForOpportunities().catch(error => {
        console.error('❌ Scheduled cross-chain scan failed:', error);
      });
    }, CROSS_CHAIN_UPDATE_INTERVAL);

    // Initialize WebSocket server
    initializeWebSocketServer();

    server.listen(PORT, '127.0.0.1', () => {
      console.log(`\n✅ Enhanced Backend server running on http://localhost:${PORT}`);
      console.log(`✅ WebSocket server running on ws://localhost:${WS_PORT}/ws`);
      console.log(`✅ Health check: http://localhost:${PORT}/health`);
      console.log(`✅ API endpoints available:`);
      console.log(`   - GET /api/opportunities`);
      console.log(`   - GET /api/opportunities/enhanced`);
      console.log(`   - GET /api/opportunities/cross-chain`);
      console.log(`   - GET /api/opportunities/comprehensive`);
      console.log(`   - GET /api/execution/queue`);
      console.log(`   - GET /api/execution/history`);
      console.log(`   - POST /api/execution/trigger/:id`);
      console.log(`   - GET /api/trades`);
      console.log(`   - GET /api/tokens`);
      console.log(`   - GET /api/tokens/top`);
      console.log(`   - GET /api/networks`);
      console.log(`   - GET /api/analytics/performance`);
      console.log(`   - GET /api/analytics/cross-chain`);
      console.log(`   - GET /api/system/health`);
      console.log(`   - GET /api/system/performance`);
      console.log(`   - GET /api/realtime/update`);
      console.log(`🔄 Ready to serve frontend requests with comprehensive arbitrage system...`);
      console.log(`📊 High-frequency updates: Token prices every ${TOKEN_UPDATE_INTERVAL / 1000} seconds`);
      console.log(`🎯 Execution queue updates every ${CROSS_CHAIN_UPDATE_INTERVAL / 1000} seconds`);
      console.log(`🌐 Comprehensive opportunity detection: Cross-chain, Intra-chain, Triangular`);
      console.log(`🔗 Supported networks: ${multiChainService.getSupportedNetworks().length} chains`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 Shutting down enhanced backend...');

  if (influxWriteApi) {
    try {
      await influxWriteApi.close();
    } catch (error) {
      console.error('Error closing InfluxDB:', error);
    }
  }

  if (redisClient) {
    try {
      await redisClient.quit();
    } catch (error) {
      console.error('Error closing Redis:', error);
    }
  }

  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 Shutting down enhanced backend...');

  if (influxWriteApi) {
    try {
      await influxWriteApi.close();
    } catch (error) {
      console.error('Error closing InfluxDB:', error);
    }
  }

  if (redisClient) {
    try {
      await redisClient.quit();
    } catch (error) {
      console.error('Error closing Redis:', error);
    }
  }

  process.exit(0);
});

// Start the server
startServer();
