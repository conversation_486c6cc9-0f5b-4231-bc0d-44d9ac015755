# MEV Arbitrage Bot - Testnet Environment Configuration
# Copy this file to .env.testnet and fill in your actual values

# =============================================================================
# DEPLOYMENT SETTINGS
# =============================================================================

# Private key for testnet deployments (DO NOT use mainnet keys!)
PRIVATE_KEY=your_testnet_private_key_here

# =============================================================================
# ETHEREUM TESTNETS
# =============================================================================

# Ethereum Goerli
GOERLI_RPC_URL=https://goerli.infura.io/v3/your_infura_key
GOERLI_ETHERSCAN_API_KEY=your_etherscan_api_key

# Ethereum Sepolia
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/your_infura_key
SEPOLIA_ETHERSCAN_API_KEY=your_etherscan_api_key

# =============================================================================
# LAYER 2 TESTNETS
# =============================================================================

# Polygon Mumbai
MUMBAI_RPC_URL=https://polygon-mumbai.infura.io/v3/your_infura_key
MUMBAI_POLYGONSCAN_API_KEY=your_polygonscan_api_key

# Arbitrum Goerli
ARBITRUM_GOERLI_RPC_URL=https://arbitrum-goerli.infura.io/v3/your_infura_key
ARBITRUM_ETHERSCAN_API_KEY=your_arbiscan_api_key

# Optimism Goerli
OPTIMISM_GOERLI_RPC_URL=https://optimism-goerli.infura.io/v3/your_infura_key
OPTIMISM_ETHERSCAN_API_KEY=your_optimistic_etherscan_api_key

# Base Goerli
BASE_GOERLI_RPC_URL=https://goerli.base.org
BASE_ETHERSCAN_API_KEY=your_basescan_api_key

# =============================================================================
# OTHER EVM TESTNETS
# =============================================================================

# BSC Testnet
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
BSC_TESTNET_API_KEY=your_bscscan_api_key

# Avalanche Fuji
AVALANCHE_FUJI_RPC_URL=https://api.avax-test.network/ext/bc/C/rpc
AVALANCHE_SNOWTRACE_API_KEY=your_snowtrace_api_key

# Fantom Testnet
FANTOM_TESTNET_RPC_URL=https://rpc.testnet.fantom.network/
FANTOM_FTMSCAN_API_KEY=your_ftmscan_api_key

# =============================================================================
# NON-EVM TESTNETS
# =============================================================================

# Solana Devnet
SOLANA_DEVNET_RPC_URL=https://api.devnet.solana.com
SOLANA_PRIVATE_KEY=your_solana_private_key_base58

# Sui Testnet
SUI_TESTNET_RPC_URL=https://fullnode.testnet.sui.io:443
SUI_PRIVATE_KEY=your_sui_private_key

# =============================================================================
# PRICE FEED APIS (for testing)
# =============================================================================

# CoinGecko API (free tier)
COINGECKO_API_KEY=your_coingecko_api_key

# CoinMarketCap API
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key

# Chainlink Price Feeds (testnet endpoints)
CHAINLINK_TESTNET_ENDPOINT=https://api.testnet.chain.link

# =============================================================================
# FLASH LOAN PROVIDERS (testnet addresses)
# =============================================================================

# Aave V3 Testnet Addresses
AAVE_V3_POOL_GOERLI=0x368EedF3f56ad10b9bC57eed4Dac65B26Bb667f6
AAVE_V3_POOL_MUMBAI=0x1758d4e6f68166C4B2d9d0F049F33dEB399Daa1F
AAVE_V3_POOL_FUJI=0xb47673b7a73D78743AFF1487AF69dBB5763F00cA

# Balancer V2 Testnet Vaults
BALANCER_VAULT_GOERLI=0xBA12222222228d8Ba445958a75a0704d566BF2C8
BALANCER_VAULT_MUMBAI=0xBA12222222228d8Ba445958a75a0704d566BF2C8

# Uniswap V3 Testnet Routers
UNISWAP_V3_ROUTER_GOERLI=0xE592427A0AEce92De3Edee1F18E0157C05861564
UNISWAP_V3_ROUTER_MUMBAI=0xE592427A0AEce92De3Edee1F18E0157C05861564

# =============================================================================
# BRIDGE CONTRACTS (testnet)
# =============================================================================

# Polygon Bridge
POLYGON_BRIDGE_GOERLI=0xBbD7cBFA79faee899Eaf900F13C9065bF03B1A74
POLYGON_BRIDGE_MUMBAI=0xfe5e5D361b2ad62c541bAb87C45a0B9B018389a2

# Arbitrum Bridge
ARBITRUM_BRIDGE_GOERLI=0x70C143928eCfFaf9F5b406f7f4fC28Dc43d68380
ARBITRUM_BRIDGE_ARBITRUM=0x6c411aD3E74De3E7Bd422b94A27770f5B86C623B

# Optimism Bridge
OPTIMISM_BRIDGE_GOERLI=0x636Af16bf2f682dD3109e60102b8E1A089FedAa8
OPTIMISM_BRIDGE_OPTIMISM=0x4200000000000000000000000000000000000010

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Test environment settings
NODE_ENV=test
LOG_LEVEL=debug

# Test database URLs
TEST_REDIS_URL=redis://localhost:6379/1
TEST_POSTGRES_URL=postgresql://localhost:5432/mev_arbitrage_test
TEST_INFLUXDB_URL=http://localhost:8086
TEST_INFLUXDB_TOKEN=test_token
TEST_INFLUXDB_ORG=test_org
TEST_INFLUXDB_BUCKET=test_bucket

# Supabase Test Project
TEST_SUPABASE_URL=https://your-test-project.supabase.co
TEST_SUPABASE_ANON_KEY=your_test_anon_key
TEST_SUPABASE_SERVICE_KEY=your_test_service_key

# =============================================================================
# PERFORMANCE TESTING
# =============================================================================

# Test duration and limits
TEST_DURATION_SECONDS=300
MAX_CONCURRENT_TESTS=10
TEST_TIMEOUT_MS=30000

# Performance targets
TARGET_LATENCY_MS=1000
TARGET_THROUGHPUT_OPS_SEC=10
TARGET_MEMORY_LIMIT_MB=500
TARGET_UPTIME_PERCENTAGE=99

# =============================================================================
# MEV PROTECTION (testnet)
# =============================================================================

# Flashbots (testnet doesn't exist, use mainnet for testing)
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
FLASHBOTS_PROTECT_URL=https://protect.flashbots.net

# MEV protection settings
ENABLE_MEV_PROTECTION=true
MEV_PROTECTION_TIMEOUT=30000
DYNAMIC_GAS_PRICING=true
GAS_PRICE_MULTIPLIER=1.1

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================

# Monitoring endpoints
MONITORING_WEBHOOK_URL=https://your-monitoring-service.com/webhook
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# Health check intervals
HEALTH_CHECK_INTERVAL_MS=5000
PERFORMANCE_MONITOR_INTERVAL_MS=10000

# =============================================================================
# RATE LIMITING
# =============================================================================

# RPC rate limits (requests per minute)
ETHEREUM_RPC_RATE_LIMIT=300
POLYGON_RPC_RATE_LIMIT=500
BSC_RPC_RATE_LIMIT=200
AVALANCHE_RPC_RATE_LIMIT=300

# API rate limits
COINGECKO_RATE_LIMIT=50
CHAINLINK_RATE_LIMIT=100

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Testnet security (less strict than mainnet)
MAX_SLIPPAGE_PERCENTAGE=5
MAX_TRADE_SIZE_USD=10000
DAILY_LOSS_LIMIT_USD=1000
EMERGENCY_STOP_ENABLED=true

# Wallet security
ENABLE_HARDWARE_WALLET=false
REQUIRE_CONFIRMATION=false

# =============================================================================
# DEPLOYMENT VERIFICATION
# =============================================================================

# Contract verification on block explorers
VERIFY_CONTRACTS=true
VERIFICATION_TIMEOUT_MS=60000

# Deployment validation
VALIDATE_DEPLOYMENT=true
RUN_POST_DEPLOYMENT_TESTS=true

# =============================================================================
# DEBUGGING
# =============================================================================

# Debug settings
DEBUG_MODE=true
VERBOSE_LOGGING=true
SAVE_DEBUG_LOGS=true
DEBUG_LOG_PATH=./logs/debug

# Transaction debugging
LOG_ALL_TRANSACTIONS=true
SAVE_FAILED_TRANSACTIONS=true
TRANSACTION_LOG_PATH=./logs/transactions
