// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./interfaces/IERC20.sol";

/**
 * @title TokenDiscovery
 * @dev Contract for managing token whitelists and blacklists for arbitrage operations
 */
contract TokenDiscovery is Ownable, ReentrancyGuard {

    struct TokenInfo {
        address tokenAddress;
        string name;
        string symbol;
        uint256 minLiquidity;
        uint8 safetyScore; // 0-100
        bool isWhitelisted;
        bool isBlacklisted;
        uint256 addedTimestamp;
        uint256 lastUpdated;
        bool isActive;
        uint256 holderCount;
        uint256 ageInDays;
    }

    struct SafetyMetrics {
        bool contractVerified;
        bool hasLiquidity;
        uint256 holderCount;
        uint256 ageInDays;
        bool hasValidMetadata;
        bool isNotPaused;
        bool noMintFunction;
        bool noBlacklist;
        uint256 transferTaxRate; // in basis points
    }

    mapping(address => TokenInfo) public tokens;
    mapping(address => bool) public authorizedCallers;
    mapping(string => address) public symbolToAddress;

    address[] public whitelistedTokens;
    address[] public blacklistedTokens;
    address[] public tokenList;
    address[] public topTokens;

    uint256 public constant MIN_LIQUIDITY_THRESHOLD = 2 ether; // Equivalent to 2 ETH
    uint256 public constant MIN_SAFETY_SCORE = 70;
    uint256 public constant MAX_TOP_TOKENS = 50;
    uint256 public constant MIN_HOLDER_COUNT = 1000;
    uint256 public constant MIN_AGE_DAYS = 30;
    uint256 public constant MAX_TRANSFER_TAX = 500; // 5% in basis points

    event TokenWhitelisted(address indexed token, string name, string symbol);
    event TokenBlacklisted(address indexed token, string reason);
    event TokenRemoved(address indexed token);
    event AuthorizedCallerAdded(address indexed caller);
    event AuthorizedCallerRemoved(address indexed caller);
    event TokenAdded(address indexed token, string symbol, bool isWhitelisted);
    event TokenUpdated(address indexed token, uint256 safetyScore);
    event SafetyScoreUpdated(address indexed token, uint256 oldScore, uint256 newScore);

    modifier onlyAuthorized() {
        require(authorizedCallers[msg.sender] || msg.sender == owner(), "Not authorized");
        _;
    }

    constructor() Ownable(msg.sender) {
        authorizedCallers[msg.sender] = true;
    }

    /**
     * @dev Add a token to the whitelist
     */
    function addToWhitelist(
        address tokenAddress,
        string memory name,
        string memory symbol,
        uint256 minLiquidity,
        uint8 safetyScore
    ) external onlyAuthorized {
        require(tokenAddress != address(0), "Invalid token address");
        require(!tokens[tokenAddress].isBlacklisted, "Token is blacklisted");
        require(minLiquidity >= MIN_LIQUIDITY_THRESHOLD, "Insufficient liquidity");
        require(safetyScore >= MIN_SAFETY_SCORE, "Safety score too low");

        if (!tokens[tokenAddress].isWhitelisted) {
            whitelistedTokens.push(tokenAddress);
        }

        tokens[tokenAddress] = TokenInfo({
            tokenAddress: tokenAddress,
            name: name,
            symbol: symbol,
            minLiquidity: minLiquidity,
            safetyScore: safetyScore,
            isWhitelisted: true,
            isBlacklisted: false,
            addedTimestamp: block.timestamp,
            lastUpdated: block.timestamp,
            isActive: true,
            holderCount: 0,
            ageInDays: 0
        });

        emit TokenWhitelisted(tokenAddress, name, symbol);
    }

    /**
     * @dev Add a token to the blacklist
     */
    function addToBlacklist(address tokenAddress, string memory reason) external onlyAuthorized {
        require(tokenAddress != address(0), "Invalid token address");

        if (tokens[tokenAddress].isWhitelisted) {
            _removeFromWhitelist(tokenAddress);
        }

        if (!tokens[tokenAddress].isBlacklisted) {
            blacklistedTokens.push(tokenAddress);
        }

        tokens[tokenAddress].isBlacklisted = true;
        tokens[tokenAddress].isWhitelisted = false;

        emit TokenBlacklisted(tokenAddress, reason);
    }

    /**
     * @dev Check if a token is whitelisted and meets criteria
     */
    function isTokenValid(address tokenAddress) external view returns (bool) {
        TokenInfo memory token = tokens[tokenAddress];
        return token.isWhitelisted &&
               !token.isBlacklisted &&
               token.safetyScore >= MIN_SAFETY_SCORE;
    }

    /**
     * @dev Get all whitelisted tokens
     */
    function getWhitelistedTokens() external view returns (address[] memory) {
        return whitelistedTokens;
    }

    /**
     * @dev Get token information
     */
    function getTokenInfo(address tokenAddress) external view returns (TokenInfo memory) {
        return tokens[tokenAddress];
    }

    /**
     * @dev Add authorized caller
     */
    function addAuthorizedCaller(address caller) external onlyOwner {
        authorizedCallers[caller] = true;
        emit AuthorizedCallerAdded(caller);
    }

    /**
     * @dev Remove authorized caller
     */
    function removeAuthorizedCaller(address caller) external onlyOwner {
        authorizedCallers[caller] = false;
        emit AuthorizedCallerRemoved(caller);
    }

    /**
     * @dev Calculate comprehensive safety score based on metrics
     */
    function calculateSafetyScore(SafetyMetrics memory metrics) public pure returns (uint256) {
        uint256 score = 0;

        // Contract verification (20 points)
        if (metrics.contractVerified) score += 20;

        // Liquidity (15 points)
        if (metrics.hasLiquidity) score += 15;

        // Holder count (15 points)
        if (metrics.holderCount > 10000) score += 15;
        else if (metrics.holderCount > 1000) score += 10;
        else if (metrics.holderCount > 100) score += 5;

        // Token age (10 points)
        if (metrics.ageInDays > 365) score += 10;
        else if (metrics.ageInDays > 180) score += 7;
        else if (metrics.ageInDays > 30) score += 5;

        // Valid metadata (10 points)
        if (metrics.hasValidMetadata) score += 10;

        // Not paused (10 points)
        if (metrics.isNotPaused) score += 10;

        // No mint function (10 points)
        if (metrics.noMintFunction) score += 10;

        // No blacklist (5 points)
        if (metrics.noBlacklist) score += 5;

        // Transfer tax penalty (subtract 2 points per 1% tax)
        if (metrics.transferTaxRate > 0) {
            uint256 penalty = (metrics.transferTaxRate * 2) / 100;
            score = score > penalty ? score - penalty : 0;
        }

        return score > 100 ? 100 : score;
    }

    /**
     * @dev Update token safety score based on metrics
     */
    function updateSafetyScore(
        address tokenAddress,
        SafetyMetrics memory metrics
    ) external onlyAuthorized {
        require(tokens[tokenAddress].tokenAddress != address(0), "Token not found");

        uint256 oldScore = tokens[tokenAddress].safetyScore;
        uint256 newScore = calculateSafetyScore(metrics);

        tokens[tokenAddress].safetyScore = uint8(newScore);
        tokens[tokenAddress].lastUpdated = block.timestamp;
        tokens[tokenAddress].holderCount = metrics.holderCount;
        tokens[tokenAddress].ageInDays = metrics.ageInDays;

        emit SafetyScoreUpdated(tokenAddress, oldScore, newScore);
        emit TokenUpdated(tokenAddress, newScore);
    }

    /**
     * @dev Get top tokens for arbitrage
     */
    function getTopTokens() external view returns (address[] memory) {
        return topTokens;
    }

    /**
     * @dev Update top tokens list
     */
    function updateTopTokens(address[] memory newTopTokens) external onlyAuthorized {
        require(newTopTokens.length <= MAX_TOP_TOKENS, "Too many tokens");
        topTokens = newTopTokens;
    }

    /**
     * @dev Get token by symbol
     */
    function getTokenBySymbol(string memory symbol) external view returns (address) {
        return symbolToAddress[symbol];
    }

    /**
     * @dev Get all tokens
     */
    function getAllTokens() external view returns (address[] memory) {
        return tokenList;
    }

    /**
     * @dev Internal function to remove token from whitelist array
     */
    function _removeFromWhitelist(address tokenAddress) internal {
        for (uint i = 0; i < whitelistedTokens.length; i++) {
            if (whitelistedTokens[i] == tokenAddress) {
                whitelistedTokens[i] = whitelistedTokens[whitelistedTokens.length - 1];
                whitelistedTokens.pop();
                break;
            }
        }
    }
}
