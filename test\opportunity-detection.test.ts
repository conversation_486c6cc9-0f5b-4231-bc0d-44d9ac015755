import { expect } from "chai";
import { ethers } from "hardhat";
import { time } from "@nomicfoundation/hardhat-network-helpers";

/**
 * Opportunity Detection and Accuracy Testing Suite
 * 
 * Comprehensive testing of arbitrage opportunity detection, accuracy measurement,
 * cross-chain opportunity identification, and profit calculation validation.
 */

interface ArbitrageOpportunity {
  id: string;
  type: "intra-chain" | "cross-chain" | "triangular";
  tokenA: string;
  tokenB: string;
  tokenC?: string; // For triangular arbitrage
  exchangeA: string;
  exchangeB: string;
  exchangeC?: string;
  priceA: number;
  priceB: number;
  priceC?: number;
  expectedProfit: number;
  profitMargin: number;
  gasEstimate: number;
  confidence: number; // 0-100
  timestamp: number;
  chainId: number;
  bridgeChainId?: number;
  bridgeFee?: number;
  bridgeTime?: number;
}

interface HistoricalData {
  timestamp: number;
  tokenA: string;
  tokenB: string;
  exchangeA: string;
  exchangeB: string;
  priceA: number;
  priceB: number;
  actualProfit: number;
  gasUsed: number;
}

interface DetectionMetrics {
  totalOpportunities: number;
  detectedOpportunities: number;
  truePositives: number;
  falsePositives: number;
  falseNegatives: number;
  accuracy: number;
  precision: number;
  recall: number;
  profitAccuracy: number;
}

class OpportunityDetectionTester {
  private historicalData: HistoricalData[] = [];
  private detectedOpportunities: ArbitrageOpportunity[] = [];
  private mockExchanges: Map<string, any> = new Map();
  private priceFeeds: Map<string, number> = new Map();

  constructor() {
    this.initializeHistoricalData();
    this.initializeMockExchanges();
  }

  private initializeHistoricalData(): void {
    // Generate realistic historical arbitrage data
    const baseTimestamp = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days ago
    
    for (let i = 0; i < 1000; i++) {
      const timestamp = baseTimestamp + (i * 60 * 60 * 1000); // Hourly data
      
      // Create various arbitrage scenarios
      this.historicalData.push({
        timestamp,
        tokenA: "WETH",
        tokenB: "USDC",
        exchangeA: "Uniswap",
        exchangeB: "SushiSwap",
        priceA: 2000 + (Math.random() - 0.5) * 100, // $1950-$2050
        priceB: 2000 + (Math.random() - 0.5) * 120, // $1940-$2060
        actualProfit: Math.random() * 200 - 50, // -$50 to $150
        gasUsed: 150000 + Math.random() * 50000, // 150k-200k gas
      });

      // Add some high-profit opportunities
      if (i % 50 === 0) {
        this.historicalData.push({
          timestamp: timestamp + 1800000, // 30 minutes later
          tokenA: "WBTC",
          tokenB: "WETH",
          exchangeA: "Uniswap",
          exchangeB: "Balancer",
          priceA: 20, // 20 ETH per BTC
          priceB: 20.5, // 20.5 ETH per BTC
          actualProfit: 150 + Math.random() * 100, // $150-$250
          gasUsed: 200000 + Math.random() * 100000,
        });
      }
    }
  }

  private initializeMockExchanges(): void {
    // Mock exchange price feeds
    this.priceFeeds.set("Uniswap_WETH_USDC", 2000);
    this.priceFeeds.set("SushiSwap_WETH_USDC", 2005);
    this.priceFeeds.set("Balancer_WETH_USDC", 1998);
    this.priceFeeds.set("Curve_USDC_USDT", 1.001);
    this.priceFeeds.set("Uniswap_WBTC_WETH", 20);
    this.priceFeeds.set("SushiSwap_WBTC_WETH", 20.1);
  }

  async detectOpportunities(
    minProfitUSD: number = 50,
    maxGasPrice: number = 50, // gwei
    confidenceThreshold: number = 80
  ): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    const currentTime = Date.now();

    // Intra-chain opportunities
    const intraChainOpps = await this.detectIntraChainOpportunities(minProfitUSD, maxGasPrice);
    opportunities.push(...intraChainOpps);

    // Cross-chain opportunities
    const crossChainOpps = await this.detectCrossChainOpportunities(minProfitUSD);
    opportunities.push(...crossChainOpps);

    // Triangular arbitrage opportunities
    const triangularOpps = await this.detectTriangularOpportunities(minProfitUSD);
    opportunities.push(...triangularOpps);

    // Filter by confidence threshold
    const filteredOpportunities = opportunities.filter(opp => opp.confidence >= confidenceThreshold);

    this.detectedOpportunities = filteredOpportunities;
    return filteredOpportunities;
  }

  private async detectIntraChainOpportunities(minProfitUSD: number, maxGasPrice: number): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    
    // WETH/USDC arbitrage between Uniswap and SushiSwap
    const uniPrice = this.priceFeeds.get("Uniswap_WETH_USDC") || 0;
    const sushiPrice = this.priceFeeds.get("SushiSwap_WETH_USDC") || 0;
    
    if (Math.abs(uniPrice - sushiPrice) > 1) { // $1 price difference
      const buyExchange = uniPrice < sushiPrice ? "Uniswap" : "SushiSwap";
      const sellExchange = uniPrice < sushiPrice ? "SushiSwap" : "Uniswap";
      const buyPrice = Math.min(uniPrice, sushiPrice);
      const sellPrice = Math.max(uniPrice, sushiPrice);
      
      const tradeSize = 10; // 10 ETH
      const grossProfit = (sellPrice - buyPrice) * tradeSize;
      const gasEstimate = 180000;
      const gasCost = (gasEstimate * maxGasPrice * 1e-9) * 2000; // Assume ETH = $2000
      const netProfit = grossProfit - gasCost - (grossProfit * 0.006); // 0.6% total fees
      
      if (netProfit >= minProfitUSD) {
        opportunities.push({
          id: `intra_${Date.now()}_1`,
          type: "intra-chain",
          tokenA: "WETH",
          tokenB: "USDC",
          exchangeA: buyExchange,
          exchangeB: sellExchange,
          priceA: buyPrice,
          priceB: sellPrice,
          expectedProfit: netProfit,
          profitMargin: (netProfit / (buyPrice * tradeSize)) * 100,
          gasEstimate,
          confidence: this.calculateConfidence(grossProfit, gasCost, "intra-chain"),
          timestamp: Date.now(),
          chainId: 1, // Ethereum
        });
      }
    }

    return opportunities;
  }

  private async detectCrossChainOpportunities(minProfitUSD: number): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    
    // ETH price difference between Ethereum and Polygon
    const ethMainnetPrice = 2000;
    const ethPolygonPrice = 2008; // 0.4% higher on Polygon
    
    const tradeSize = 5; // 5 ETH
    const grossProfit = (ethPolygonPrice - ethMainnetPrice) * tradeSize;
    const bridgeFee = 0.01 * ethMainnetPrice; // $20 bridge fee
    const bridgeTime = 600; // 10 minutes
    const gasEstimate = 250000;
    const gasCost = (gasEstimate * 30 * 1e-9) * ethMainnetPrice; // 30 gwei
    const netProfit = grossProfit - bridgeFee - gasCost;
    
    if (netProfit >= minProfitUSD) {
      opportunities.push({
        id: `cross_${Date.now()}_1`,
        type: "cross-chain",
        tokenA: "WETH",
        tokenB: "WETH",
        exchangeA: "Uniswap",
        exchangeB: "QuickSwap",
        priceA: ethMainnetPrice,
        priceB: ethPolygonPrice,
        expectedProfit: netProfit,
        profitMargin: (netProfit / (ethMainnetPrice * tradeSize)) * 100,
        gasEstimate,
        confidence: this.calculateConfidence(grossProfit, bridgeFee + gasCost, "cross-chain"),
        timestamp: Date.now(),
        chainId: 1, // Ethereum
        bridgeChainId: 137, // Polygon
        bridgeFee,
        bridgeTime,
      });
    }

    return opportunities;
  }

  private async detectTriangularOpportunities(minProfitUSD: number): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    
    // ETH -> USDC -> USDT -> ETH triangular arbitrage
    const ethUsdcPrice = 2000; // 1 ETH = 2000 USDC
    const usdcUsdtPrice = 1.001; // 1 USDC = 1.001 USDT
    const usdtEthPrice = 1 / 2005; // 1 USDT = 1/2005 ETH
    
    const startAmount = 10; // 10 ETH
    const step1 = startAmount * ethUsdcPrice; // ETH to USDC
    const step2 = step1 * usdcUsdtPrice; // USDC to USDT
    const step3 = step2 * usdtEthPrice; // USDT to ETH
    
    const grossProfit = (step3 - startAmount) * 2000; // Profit in USD
    const gasEstimate = 300000; // Higher gas for 3 swaps
    const gasCost = (gasEstimate * 40 * 1e-9) * 2000;
    const totalFees = grossProfit * 0.009; // 0.9% total fees (3 x 0.3%)
    const netProfit = grossProfit - gasCost - totalFees;
    
    if (netProfit >= minProfitUSD) {
      opportunities.push({
        id: `triangular_${Date.now()}_1`,
        type: "triangular",
        tokenA: "WETH",
        tokenB: "USDC",
        tokenC: "USDT",
        exchangeA: "Uniswap",
        exchangeB: "Curve",
        exchangeC: "Uniswap",
        priceA: ethUsdcPrice,
        priceB: usdcUsdtPrice,
        priceC: 1 / usdtEthPrice,
        expectedProfit: netProfit,
        profitMargin: (netProfit / (startAmount * 2000)) * 100,
        gasEstimate,
        confidence: this.calculateConfidence(grossProfit, gasCost + totalFees, "triangular"),
        timestamp: Date.now(),
        chainId: 1,
      });
    }

    return opportunities;
  }

  private calculateConfidence(grossProfit: number, costs: number, type: string): number {
    let baseConfidence = 50;
    
    // Profit margin confidence
    const profitMargin = (grossProfit - costs) / grossProfit;
    if (profitMargin > 0.5) baseConfidence += 30;
    else if (profitMargin > 0.3) baseConfidence += 20;
    else if (profitMargin > 0.1) baseConfidence += 10;
    
    // Type-specific adjustments
    switch (type) {
      case "intra-chain":
        baseConfidence += 20; // More reliable
        break;
      case "cross-chain":
        baseConfidence -= 10; // Bridge risks
        break;
      case "triangular":
        baseConfidence -= 15; // More complex
        break;
    }
    
    // Market volatility adjustment
    const volatilityPenalty = Math.random() * 10; // Simulate market conditions
    baseConfidence -= volatilityPenalty;
    
    return Math.max(0, Math.min(100, baseConfidence));
  }

  validateProfitCalculations(opportunity: ArbitrageOpportunity): {
    isValid: boolean;
    calculatedProfit: number;
    deviation: number;
  } {
    let calculatedProfit = 0;
    
    switch (opportunity.type) {
      case "intra-chain":
        const tradeSize = 10; // Assume 10 ETH
        const grossProfit = (opportunity.priceB - opportunity.priceA) * tradeSize;
        const gasCost = (opportunity.gasEstimate * 30 * 1e-9) * 2000;
        const fees = grossProfit * 0.006;
        calculatedProfit = grossProfit - gasCost - fees;
        break;
        
      case "cross-chain":
        const crossTradeSize = 5;
        const crossGrossProfit = (opportunity.priceB - opportunity.priceA) * crossTradeSize;
        const crossGasCost = (opportunity.gasEstimate * 30 * 1e-9) * 2000;
        calculatedProfit = crossGrossProfit - (opportunity.bridgeFee || 0) - crossGasCost;
        break;
        
      case "triangular":
        // Complex calculation for triangular arbitrage
        const startAmount = 10;
        const step1 = startAmount * opportunity.priceA;
        const step2 = step1 * (opportunity.priceB || 1);
        const step3 = step2 * (1 / (opportunity.priceC || 1));
        const triGrossProfit = (step3 - startAmount) * 2000;
        const triGasCost = (opportunity.gasEstimate * 40 * 1e-9) * 2000;
        const triFees = triGrossProfit * 0.009;
        calculatedProfit = triGrossProfit - triGasCost - triFees;
        break;
    }
    
    const deviation = Math.abs(calculatedProfit - opportunity.expectedProfit) / opportunity.expectedProfit;
    const isValid = deviation < 0.05; // 5% tolerance
    
    return { isValid, calculatedProfit, deviation };
  }

  measureDetectionAccuracy(
    detectedOpportunities: ArbitrageOpportunity[],
    actualOpportunities: HistoricalData[]
  ): DetectionMetrics {
    let truePositives = 0;
    let falsePositives = 0;
    let falseNegatives = 0;
    let profitAccuracySum = 0;
    let validProfitCalculations = 0;

    // Match detected opportunities with actual historical data
    for (const detected of detectedOpportunities) {
      const matchingActual = actualOpportunities.find(actual => 
        actual.tokenA === detected.tokenA &&
        actual.tokenB === detected.tokenB &&
        Math.abs(actual.timestamp - detected.timestamp) < 3600000 // Within 1 hour
      );

      if (matchingActual) {
        if (matchingActual.actualProfit >= 50) { // Profitable opportunity
          truePositives++;
          
          // Calculate profit accuracy
          const profitDeviation = Math.abs(detected.expectedProfit - matchingActual.actualProfit) / matchingActual.actualProfit;
          profitAccuracySum += (1 - profitDeviation);
          validProfitCalculations++;
        } else {
          falsePositives++; // Detected as profitable but wasn't
        }
      } else {
        falsePositives++; // No matching actual opportunity
      }
    }

    // Count missed opportunities
    for (const actual of actualOpportunities) {
      if (actual.actualProfit >= 50) {
        const wasDetected = detectedOpportunities.some(detected =>
          detected.tokenA === actual.tokenA &&
          detected.tokenB === actual.tokenB &&
          Math.abs(detected.timestamp - actual.timestamp) < 3600000
        );
        
        if (!wasDetected) {
          falseNegatives++;
        }
      }
    }

    const totalOpportunities = truePositives + falseNegatives;
    const detectedOpportunitiesCount = truePositives + falsePositives;
    
    const accuracy = totalOpportunities > 0 ? truePositives / totalOpportunities : 0;
    const precision = detectedOpportunitiesCount > 0 ? truePositives / detectedOpportunitiesCount : 0;
    const recall = totalOpportunities > 0 ? truePositives / totalOpportunities : 0;
    const profitAccuracy = validProfitCalculations > 0 ? profitAccuracySum / validProfitCalculations : 0;

    return {
      totalOpportunities,
      detectedOpportunities: detectedOpportunitiesCount,
      truePositives,
      falsePositives,
      falseNegatives,
      accuracy,
      precision,
      recall,
      profitAccuracy,
    };
  }

  getHistoricalData(): HistoricalData[] {
    return this.historicalData;
  }

  simulateMarketConditions(volatility: "low" | "medium" | "high"): void {
    const multiplier = volatility === "low" ? 0.5 : volatility === "medium" ? 1.0 : 2.0;
    
    // Adjust price feeds to simulate market conditions
    for (const [key, price] of this.priceFeeds.entries()) {
      const variation = (Math.random() - 0.5) * 0.02 * multiplier; // ±1% to ±4%
      this.priceFeeds.set(key, price * (1 + variation));
    }
  }
}

describe("Opportunity Detection and Accuracy Testing", function () {
  let opportunityDetector: OpportunityDetectionTester;

  before(async function () {
    opportunityDetector = new OpportunityDetectionTester();
  });

  describe("Opportunity Detection", function () {
    it("Should detect intra-chain arbitrage opportunities", async function () {
      const opportunities = await opportunityDetector.detectOpportunities(50, 50, 70);
      
      const intraChainOpps = opportunities.filter(opp => opp.type === "intra-chain");
      expect(intraChainOpps.length).to.be.greaterThan(0);
      
      intraChainOpps.forEach(opp => {
        expect(opp.expectedProfit).to.be.greaterThanOrEqual(50);
        expect(opp.confidence).to.be.greaterThanOrEqual(70);
        expect(opp.chainId).to.equal(1);
      });
      
      console.log(`Detected ${intraChainOpps.length} intra-chain opportunities`);
    });

    it("Should detect cross-chain arbitrage opportunities", async function () {
      const opportunities = await opportunityDetector.detectOpportunities(50, 50, 60);
      
      const crossChainOpps = opportunities.filter(opp => opp.type === "cross-chain");
      expect(crossChainOpps.length).to.be.greaterThan(0);
      
      crossChainOpps.forEach(opp => {
        expect(opp.expectedProfit).to.be.greaterThanOrEqual(50);
        expect(opp.bridgeChainId).to.exist;
        expect(opp.bridgeFee).to.be.greaterThan(0);
        expect(opp.bridgeTime).to.be.greaterThan(0);
      });
      
      console.log(`Detected ${crossChainOpps.length} cross-chain opportunities`);
    });

    it("Should detect triangular arbitrage opportunities", async function () {
      const opportunities = await opportunityDetector.detectOpportunities(50, 50, 60);
      
      const triangularOpps = opportunities.filter(opp => opp.type === "triangular");
      expect(triangularOpps.length).to.be.greaterThan(0);
      
      triangularOpps.forEach(opp => {
        expect(opp.expectedProfit).to.be.greaterThanOrEqual(50);
        expect(opp.tokenC).to.exist;
        expect(opp.exchangeC).to.exist;
        expect(opp.priceC).to.exist;
      });
      
      console.log(`Detected ${triangularOpps.length} triangular opportunities`);
    });
  });

  describe("Profit Calculation Validation", function () {
    it("Should validate profit calculations accurately", async function () {
      const opportunities = await opportunityDetector.detectOpportunities(50, 50, 70);
      
      let validCalculations = 0;
      let totalDeviations = 0;
      
      for (const opportunity of opportunities) {
        const validation = opportunityDetector.validateProfitCalculations(opportunity);
        
        if (validation.isValid) {
          validCalculations++;
        }
        
        totalDeviations += validation.deviation;
        
        console.log(`${opportunity.type} opportunity: Expected $${opportunity.expectedProfit.toFixed(2)}, Calculated $${validation.calculatedProfit.toFixed(2)}, Deviation: ${(validation.deviation * 100).toFixed(2)}%`);
      }
      
      const accuracyRate = validCalculations / opportunities.length;
      const averageDeviation = totalDeviations / opportunities.length;
      
      expect(accuracyRate).to.be.greaterThan(0.9); // 90% accuracy
      expect(averageDeviation).to.be.lessThan(0.05); // <5% average deviation
      
      console.log(`Profit calculation accuracy: ${(accuracyRate * 100).toFixed(1)}%`);
      console.log(`Average deviation: ${(averageDeviation * 100).toFixed(2)}%`);
    });

    it("Should handle edge cases in profit calculations", async function () {
      // Test with very small profits
      const smallProfitOpportunity: ArbitrageOpportunity = {
        id: "test_small",
        type: "intra-chain",
        tokenA: "WETH",
        tokenB: "USDC",
        exchangeA: "Uniswap",
        exchangeB: "SushiSwap",
        priceA: 2000,
        priceB: 2001, // Only $1 difference
        expectedProfit: 5, // Very small profit
        profitMargin: 0.025,
        gasEstimate: 150000,
        confidence: 60,
        timestamp: Date.now(),
        chainId: 1,
      };
      
      const validation = opportunityDetector.validateProfitCalculations(smallProfitOpportunity);
      expect(validation.calculatedProfit).to.be.lessThan(50); // Should be unprofitable after costs
    });
  });

  describe("Detection Accuracy Measurement", function () {
    it("Should achieve >90% detection accuracy for profitable opportunities", async function () {
      const historicalData = opportunityDetector.getHistoricalData();
      const detectedOpportunities = await opportunityDetector.detectOpportunities(50, 50, 70);
      
      const metrics = opportunityDetector.measureDetectionAccuracy(detectedOpportunities, historicalData);
      
      expect(metrics.accuracy).to.be.greaterThan(0.9); // >90% accuracy
      expect(metrics.profitAccuracy).to.be.greaterThan(0.8); // >80% profit accuracy
      
      console.log("Detection Metrics:");
      console.log(`  Accuracy: ${(metrics.accuracy * 100).toFixed(1)}%`);
      console.log(`  Precision: ${(metrics.precision * 100).toFixed(1)}%`);
      console.log(`  Recall: ${(metrics.recall * 100).toFixed(1)}%`);
      console.log(`  Profit Accuracy: ${(metrics.profitAccuracy * 100).toFixed(1)}%`);
      console.log(`  True Positives: ${metrics.truePositives}`);
      console.log(`  False Positives: ${metrics.falsePositives}`);
      console.log(`  False Negatives: ${metrics.falseNegatives}`);
    });

    it("Should maintain accuracy under different market conditions", async function () {
      const conditions = ["low", "medium", "high"] as const;
      
      for (const condition of conditions) {
        opportunityDetector.simulateMarketConditions(condition);
        
        const opportunities = await opportunityDetector.detectOpportunities(50, 50, 70);
        const historicalData = opportunityDetector.getHistoricalData();
        const metrics = opportunityDetector.measureDetectionAccuracy(opportunities, historicalData);
        
        expect(metrics.accuracy).to.be.greaterThan(0.8); // Maintain >80% accuracy
        
        console.log(`${condition} volatility - Accuracy: ${(metrics.accuracy * 100).toFixed(1)}%`);
      }
    });
  });

  describe("Performance and Efficiency", function () {
    it("Should detect opportunities within performance targets", async function () {
      const startTime = Date.now();
      
      const opportunities = await opportunityDetector.detectOpportunities(50, 50, 70);
      
      const endTime = Date.now();
      const detectionTime = endTime - startTime;
      
      expect(detectionTime).to.be.lessThan(5000); // <5 seconds
      expect(opportunities.length).to.be.greaterThan(0);
      
      console.log(`Detection completed in ${detectionTime}ms`);
      console.log(`Found ${opportunities.length} opportunities`);
    });

    it("Should handle high-frequency detection efficiently", async function () {
      const iterations = 10;
      const startTime = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await opportunityDetector.detectOpportunities(50, 50, 70);
      }
      
      const endTime = Date.now();
      const averageTime = (endTime - startTime) / iterations;
      
      expect(averageTime).to.be.lessThan(1000); // <1 second average
      
      console.log(`Average detection time: ${averageTime.toFixed(0)}ms`);
    });
  });
});
