#!/usr/bin/env node

/**
 * MEV Arbitrage Bot System Startup Script
 * Ensures all databases are properly initialized and system is ready
 */

import { spawn } from 'child_process';
import { createClient } from 'redis';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';
import { InfluxDB } from '@influxdata/influxdb-client';
import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

class SystemStarter {
  constructor() {
    this.services = {
      redis: false,
      supabase: false,
      influxdb: false,
      postgres: false
    };
  }

  async startSystem() {
    console.log('🚀 Starting MEV Arbitrage Bot System...\n');

    try {
      await this.checkDatabaseConnections();
      await this.initializeDatabaseSchemas();
      await this.startBackendService();
      
      console.log('\n🎉 MEV Arbitrage Bot System started successfully!');
      console.log('📊 Dashboard: http://localhost:3001');
      console.log('🔌 WebSocket: ws://localhost:8080/ws');
      console.log('📈 Monitoring: Check /api/system/health for status');
      
    } catch (error) {
      console.error('❌ System startup failed:', error);
      process.exit(1);
    }
  }

  async checkDatabaseConnections() {
    console.log('🔍 Checking database connections...\n');

    await Promise.all([
      this.checkRedis(),
      this.checkSupabase(),
      this.checkInfluxDB(),
      this.checkPostgreSQL()
    ]);

    const connectedServices = Object.values(this.services).filter(Boolean).length;
    const totalServices = Object.keys(this.services).length;

    console.log(`\n📊 Database Status: ${connectedServices}/${totalServices} connected`);
    
    if (connectedServices < totalServices) {
      console.log('⚠️ Some databases are not connected. System will continue with available services.');
    }
  }

  async checkRedis() {
    try {
      console.log('🔗 Connecting to Redis Cloud...');
      
      const redisClient = createClient({
        username: process.env.REDIS_CLOUD_USERNAME || 'default',
        password: process.env.REDIS_CLOUD_PASSWORD,
        socket: {
          host: process.env.REDIS_CLOUD_HOST,
          port: parseInt(process.env.REDIS_CLOUD_PORT) || 11734
        }
      });

      await redisClient.connect();
      await redisClient.ping();
      await redisClient.quit();
      
      console.log('✅ Redis Cloud connected successfully');
      this.services.redis = true;
    } catch (error) {
      console.error('❌ Redis connection failed:', error.message);
      this.services.redis = false;
    }
  }

  async checkSupabase() {
    try {
      console.log('🔗 Connecting to Supabase...');
      
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
        throw new Error('Supabase credentials not found in environment');
      }

      const supabase = createSupabaseClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );

      // Test connection with a simple query
      const { data, error } = await supabase.from('tokens').select('count').limit(1);
      
      if (error && !error.message.includes('relation "tokens" does not exist')) {
        throw error;
      }
      
      console.log('✅ Supabase connected successfully');
      this.services.supabase = true;
    } catch (error) {
      console.error('❌ Supabase connection failed:', error.message);
      this.services.supabase = false;
    }
  }

  async checkInfluxDB() {
    try {
      console.log('🔗 Connecting to InfluxDB...');
      
      if (!process.env.INFLUXDB_URL || !process.env.INFLUXDB_TOKEN) {
        throw new Error('InfluxDB credentials not found in environment');
      }

      const influxDB = new InfluxDB({
        url: process.env.INFLUXDB_URL,
        token: process.env.INFLUXDB_TOKEN
      });

      const queryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG);
      
      // Test with a simple query
      const query = `from(bucket: "${process.env.INFLUXDB_BUCKET}") |> range(start: -1m) |> limit(n: 1)`;
      
      await new Promise((resolve, reject) => {
        let hasData = false;
        queryApi.queryRows(query, {
          next: () => { hasData = true; },
          error: (error) => {
            // Ignore "no data" errors as they're expected for new installations
            if (error.message.includes('no data')) {
              resolve();
            } else {
              reject(error);
            }
          },
          complete: () => resolve()
        });
      });
      
      console.log('✅ InfluxDB connected successfully');
      this.services.influxdb = true;
    } catch (error) {
      console.error('❌ InfluxDB connection failed:', error.message);
      this.services.influxdb = false;
    }
  }

  async checkPostgreSQL() {
    try {
      console.log('🔗 Connecting to PostgreSQL...');
      
      if (!process.env.POSTGRES_URL) {
        throw new Error('PostgreSQL URL not found in environment');
      }

      const { Pool } = pg;
      const pool = new Pool({
        connectionString: process.env.POSTGRES_URL,
        ssl: false
      });

      const client = await pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      await pool.end();
      
      console.log('✅ PostgreSQL connected successfully');
      this.services.postgres = true;
    } catch (error) {
      console.error('❌ PostgreSQL connection failed:', error.message);
      this.services.postgres = false;
    }
  }

  async initializeDatabaseSchemas() {
    console.log('\n🔧 Initializing database schemas...');

    if (this.services.supabase) {
      await this.initializeSupabaseSchema();
    }

    if (this.services.postgres) {
      await this.initializePostgreSQLSchema();
    }

    console.log('✅ Database schemas initialized');
  }

  async initializeSupabaseSchema() {
    try {
      const supabase = createSupabaseClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );

      // Create tables if they don't exist (this would normally be done via migrations)
      console.log('📋 Supabase schema ready');
    } catch (error) {
      console.error('❌ Supabase schema initialization failed:', error.message);
    }
  }

  async initializePostgreSQLSchema() {
    try {
      const { Pool } = pg;
      const pool = new Pool({
        connectionString: process.env.POSTGRES_URL,
        ssl: false
      });

      const client = await pool.connect();
      
      // Create arbitrage_executions table if it doesn't exist
      await client.query(`
        CREATE TABLE IF NOT EXISTS arbitrage_executions (
          id VARCHAR(255) PRIMARY KEY,
          opportunity_id VARCHAR(255),
          type VARCHAR(50),
          assets JSONB,
          exchanges JSONB,
          networks JSONB,
          expected_profit DECIMAL(15,6),
          actual_profit DECIMAL(15,6),
          status VARCHAR(20),
          execution_time INTEGER,
          gas_used DECIMAL(15,6),
          slippage_impact DECIMAL(15,6),
          error_message TEXT,
          executed_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `);

      client.release();
      await pool.end();
      
      console.log('📋 PostgreSQL schema ready');
    } catch (error) {
      console.error('❌ PostgreSQL schema initialization failed:', error.message);
    }
  }

  async startBackendService() {
    console.log('\n🚀 Starting backend service...');

    return new Promise((resolve, reject) => {
      const backend = spawn('node', ['enhanced-backend.mjs'], {
        stdio: 'inherit',
        env: { ...process.env }
      });

      backend.on('error', (error) => {
        reject(new Error(`Backend startup failed: ${error.message}`));
      });

      // Give the backend time to start
      setTimeout(() => {
        if (backend.pid) {
          resolve();
        } else {
          reject(new Error('Backend failed to start within timeout'));
        }
      }, 5000);

      // Handle graceful shutdown
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down system...');
        backend.kill('SIGTERM');
        process.exit(0);
      });

      process.on('SIGTERM', () => {
        console.log('\n🛑 Shutting down system...');
        backend.kill('SIGTERM');
        process.exit(0);
      });
    });
  }
}

// Run startup if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const starter = new SystemStarter();
  starter.startSystem().catch(console.error);
}

export default SystemStarter;
